server:
  port: 8080

# 운영시 삭제
decorator:
  datasource:
    p6spy:
      enable-logging: true

spring:
  # 404처리
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      static-locations: META-INF:/resources, classpath:/resources, classpath:/static, classpath:/static/dist
  # Database
  datasource:
    driver-class-name: org.postgresql.Driver
#    url: *************************************************
#    username: postgres
#    password: postgres
  url: *************************************************
  username: dz_ai_labeling
  password: ITisap@ssw0rd
  # File
  servlet:
    multipart:
      enabled: true #업로드 지원여부
      file-size-threshold: 0B #메모리에 기록되는 값
#      location: /home/<USER>/image_files #저장 공간
      max-file-size: 10MB #파일 최대 사이즈
      max-request-size: 100MB #요청 최대 사이즈
  file:
    upload:
      path: /home/<USER>/storage/ # LOCAL C:/data/file/

  jpa:
    hibernate:
      ddl-auto: none
      # naming:
        # physical-strategy : 자동으로 대문자로 변환
        # physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        # implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    database: postgresql
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false
    properties:
      hibernate:
        default_batch_fetch_size: 100
        format_sql: true
        show_sql: true

logging.level:
  org:
    hibernate:
      SQL: debug #콘솔에 남기는게 아니라 로그로 남음.
      type: trace #바인딩된 파라미터까지 볼 수 있음
  kr:
    co:
      digitalzone: debug

keys:
  password:
    secret: MyqdkWdVY62mUgn7aY0c43EhNQEwmF2At5a3SoTEMCpa9PmrWOqP0mX3w2nbuqmX
    iteration-count: 300000
    salt-length: 64
  security:
    access:
      expiration_time: 30
      secret: MyqdkWdVY62mUgn7aY0c43EhNQEwmF2At5a3SoTEMCpa9PmrWOqP0mX3w2nbuqmX
