package kr.co.digitalzone.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrawlingCountDto {

    @JsonProperty("job_id")
    private String jobId;

    private String source;
    private String keyword;

    @JsonProperty("target_count")
    private Integer targetCount;

    @JsonProperty("collected_count")
    private Long collectedCount;

    // JPA 쿼리용 생성자 (collected_count 없이)
    public CrawlingCountDto(String source, String keyword, Integer targetCount) {
        this.source = source;
        this.keyword = keyword;
        this.targetCount = targetCount;
        this.collectedCount = 0L; // 기본값
    }
}