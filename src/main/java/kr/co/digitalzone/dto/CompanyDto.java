//package kr.co.digitalzone.dto;
//
//import com.fasterxml.jackson.annotation.JsonFormat;
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//import kr.co.digitalzone.entity.comm_company;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//public class CompanyDto {
//  private String companyId;
//  private String companyName;
//  private String companyPhone;
//  private String department;
//  private String managerName;
//  private String managerPhone;
//  private String managerEmail;
//  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//  private Date registeredAt;
//  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//  private Date updatedAt;
//  private String registerIp;
//  private String updaterIp;
//  private String registeredBy;
//  private String updatedBy;
//  private String status;
//
//  // comm_company -> CompanyDto
//  public CompanyDto(comm_company commcompany) {
//    this.companyId = commcompany.getCompanyId();
//    this.companyName = commcompany.getCompanyName();
//    this.companyPhone = commcompany.getCompanyPhone();
//    this.department = commcompany.getDepartment();
//    this.managerName =  commcompany.getManagerName();
//    this.managerPhone = commcompany.getManagerPhone();
//    this.managerEmail = commcompany.getManagerEmail();
//    this.registeredAt = commcompany.getRegisteredAt();
//    this.updatedAt =  commcompany.getUpdatedAt();
//    this.registerIp =  commcompany.getRegisterIp();
//    this.updaterIp =  commcompany.getUpdaterIp();
//    this.registeredBy =  commcompany.getRegisteredBy();
//    this.updatedBy =  commcompany.getUpdatedBy();
//    this.status =  commcompany.getStatus();
//  }
//
//  public comm_company toEntity() {
//    return comm_company.builder()
//        .companyId(companyId)
//        .companyName(companyName)
//        .companyPhone(companyPhone)
//        .department(department)
//        .managerName(managerName)
//        .managerPhone(managerPhone)
//        .managerEmail(managerEmail)
//        .registeredAt(registeredAt)
//        .updatedAt(updatedAt)
//        .registerIp(registerIp)
//        .updaterIp(updaterIp)
//        .registeredBy(registeredBy)
//        .updatedBy(updatedBy)
//        .status(status)
//        .build();
//  }
//
//  // List<Entity> To List<Dto>
//  public static List<CompanyDto> listEntityToListDto(List<comm_company> entityList) {
//    return entityList.stream()
//        .map(CompanyDto::new)
//        .collect(Collectors.toList());
//  }
//}