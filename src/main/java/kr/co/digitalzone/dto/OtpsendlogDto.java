//package kr.co.digitalzone.dto;
//
//import java.util.Date;
//import kr.co.digitalzone.entity.comm_log_otpsend;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.springframework.format.annotation.DateTimeFormat;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//public class OtpsendlogDto {
//
//  private int id;
//  private String userId;
//  private String otpPin;
//  private String verified;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date registeredAt;
//  private String registerIp;
//  private String updaterIp;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date updatedAt;
//  private String registeredBy;
//  private String updatedBy;
//
//  public OtpsendlogDto(comm_log_otpsend commlogotpsend) {
//    this.id = commlogotpsend.getId();
//    this.userId = commlogotpsend.getUserId();
//    this.otpPin = commlogotpsend.getOtpPin();
//    this.verified = commlogotpsend.getVerified();
//    this.registeredAt = commlogotpsend.getRegisteredAt();
//    this.registerIp = commlogotpsend.getRegisterIp();
//    this.updaterIp = commlogotpsend.getUpdaterIp();
//    this.updatedAt = commlogotpsend.getUpdatedAt();
//    this.registeredBy = commlogotpsend.getRegisteredBy();
//    this.updatedBy = commlogotpsend.getUpdatedBy();
//  }
//
//  public comm_log_otpsend toEntity() {
//    return comm_log_otpsend.builder()
//        .id(id)
//        .userId(userId)
//        .otpPin(otpPin)
//        .verified(verified)
//        .registeredAt(registeredAt)
//        .registerIp(registerIp)
//        .updaterIp(updaterIp)
//        .updatedAt(updatedAt)
//        .registeredBy(registeredBy)
//        .updatedBy(updatedBy)
//        .build();
//  }
//}
