//package kr.co.digitalzone.dto;
//
//import jakarta.persistence.Id;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//import org.hibernate.annotations.DynamicInsert;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@DynamicInsert
//public class MemlogrecDto {
//    @Id
//    //@GeneratedValue(strategy = GenerationType.AUTO)
//    private int id;
//    private String userId;
//    private String status;
//    private Date registeredAt;
//}
