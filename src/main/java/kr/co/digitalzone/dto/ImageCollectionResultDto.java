package kr.co.digitalzone.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImageCollectionResultDto {

    @NotBlank(message = "소스는 필수입니다")
    private String source;

    @NotBlank(message = "키워드는 필수입니다")
    private String keyword;

    @NotBlank
    private String status;

}
