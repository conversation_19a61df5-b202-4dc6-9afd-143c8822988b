//package kr.co.digitalzone.dto;
//
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.annotation.JsonInclude.Include;
//import java.util.Date;
//import kr.co.digitalzone.entity.comm_log_emailsend;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@JsonInclude(Include.NON_NULL)
//public class EmailsendlogDto {
//  private int id;
//  private String status;
//  private String typeCd;
//  private int templateId;
//  private String emailFrom;
//  private String emailTo;
//  private String subject;
//  private String content;
//  private Date sentAt;
//  private String registeredBy;
//  private String registeredIp;
//  private Date registeredAt;
//  private String updatedBy;
//  private String updatedIp;
//  private Date updatedAt;
//
//  public comm_log_emailsend toEntity() {
//    return comm_log_emailsend.builder()
//        .id(id)
//        .status(status)
//        .typeCd(typeCd)
//        .templateId(templateId)
//        .emailFrom(emailFrom)
//        .emailTo(emailTo)
//        .subject(subject)
//        .content(content)
//        .sentAt(sentAt)
//        .registeredBy(registeredBy)
//        .registeredIp(registeredIp)
//        .registeredAt(registeredAt)
//        .updatedBy(updatedBy)
//        .updatedIp(updatedIp)
//        .updatedAt(updatedAt)
//        .build();
//  }
//
//}
