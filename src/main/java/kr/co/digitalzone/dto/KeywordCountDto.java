package kr.co.digitalzone.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
//@AllArgsConstructor
@NoArgsConstructor
public class KeywordCountDto {
    
    private String keyword;
    
    @JsonProperty("collected_count")
    private Long collectedCount;
    
    // JPA 쿼리용 생성자
    public KeywordCountDto(String keyword, Long collectedCount) {
        this.keyword = keyword;
        this.collectedCount = collectedCount;
    }
}
