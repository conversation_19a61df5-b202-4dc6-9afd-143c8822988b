
package kr.co.digitalzone.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import kr.co.digitalzone.entity.comm_users;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MembersDto implements Serializable {
  private String id;
  private String email;
  private String verified;
  private String verifiedKey;
//  private String approved;
//  private String name;
//  private String phone;
  private String password;
//  private String password_1;
//  private String password_2;
//  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
//  private Date passwordChangedAt;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date registeredAt;
  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date updatedAt;
  private String registerIp;
  private String updaterIp;
  private String registeredBy;
  private String updatedBy;
  private String status;
//  private String companyId;
//  private String companyName;
//  private String department;
//  private String companyPhone;
//  private String checkLogin;
//  private String role;

  // comm_users -> MembersDto
  public MembersDto(comm_users commusers) {
    this.id = commusers.getId();
    this.email = commusers.getEmail();
    this.verified = commusers.getVerified();

//    this.approved = commusers.getApproved();

    this.password = commusers.getPassword();
//    this.password_1 = commusers.getPassword_1();
//    this.password_2 = commusers.getPassword_2();
//    this.passwordChangedAt = commusers.getPasswordChangedAt();
    this.registeredAt = commusers.getRegisteredAt();
    this.updatedAt = commusers.getUpdatedAt();
    this.registerIp = commusers.getRegisterIp();
    this.updaterIp = commusers.getUpdaterIp();
    this.registeredBy = commusers.getRegisteredBy();
    this.updatedBy = commusers.getUpdatedBy();
    this.status = commusers.getStatus();
//    this.companyId = commusers.getCompanyId();
//    this.department = commusers.getDepartment();
//    this.checkLogin = commusers.getCheckLogin();
//    this.role = commusers.getRole();
  }

  public MembersDto(String id, String email, String verified, String password, Date registeredAt, Date updatedAt, String registerIp, String updaterIp, String registeredBy, String updatedBy,String  status) {
    this.id = id;
    this.email = email;
    this.verified = verified;
//    this.approved = approved;
    this.password = password;
    this.registeredAt = registeredAt;
    this.updatedAt = updatedAt;
    this.registerIp = registerIp;
    this.updaterIp = updaterIp;
    this.registeredBy = registeredBy;
    this.updatedBy = updatedBy;
    this.status = status;
//    this.checkLogin = checkLogin;
  }

  // List<MembersDto> 타입의 ApiResponse + 마스킹 처리
  public MembersDto toListApiResponse() {
    return MembersDto.builder()
        .id(id)
        .email(email)
        .verified(verified)
//        .approved(approved)
//        .passwordChangedAt(passwordChangedAt)
        .registeredAt(registeredAt)
        .updatedAt(updatedAt)
        .registerIp(registerIp)
        .updaterIp(updaterIp)
        .registeredBy(registeredBy)
        .updatedBy(updatedBy)
//        .status(status)
//        .companyId(companyId)
//        .companyName(companyName)
//        .department(department)
//        .companyPhone(companyPhone)
//        .checkLogin(checkLogin)
//        .role(role)
        .build();
  }

  public MembersDto toApiResponse() {
    return MembersDto.builder()
        .id(id)
        .email(registeredBy)
        .verified(verified)
//        .approved(approved)
//        .passwordChangedAt(passwordChangedAt)
        .registeredAt(registeredAt)
        .updatedAt(updatedAt)
        .registerIp(registerIp)
        .updaterIp(updaterIp)
        .registeredBy(registeredBy)
        .updatedBy(updatedBy)
//        .status(status)
//        .companyId(companyId)
//        .companyName(companyName)
//        .department(department)
//        .companyPhone(companyPhone)
//        .checkLogin(checkLogin)
//        .role(role)
        .build();
  }

  // List<MembersDto> 타입의 ApiResponse
  public static List<MembersDto> toApiResponse(List<MembersDto> dtoList) {
    return dtoList.stream()
        .map(MembersDto::toApiResponse)
        .collect(Collectors.toList());
  }

  // List<MembersDto> 타입의 ApiResponse 마스킹처리 버전
  public static List<MembersDto> toListApiResponse(List<MembersDto> dtoList) {
    return dtoList.stream()
        .map(MembersDto::toListApiResponse)
        .collect(Collectors.toList());
  }

  public MembersDto toApiResponseJoin() {
    return MembersDto.builder()
        .id(id)
        .email(email)
        .verifiedKey(verifiedKey)
        .build();
  }

  public MembersDto(String id, String email, String verifiedKey) {
    this.id = id;
    this.email = email;
    this.verifiedKey = verifiedKey;
  }

  public comm_users toEntity() {
    return comm_users.builder()
        .id(id)
        .email(email)
        .verified(verified)
//        .approved(approved)
        .password(password)
//        .password_1(password_1)
//        .password_2(password_2)
//        .passwordChangedAt(passwordChangedAt)
        .registeredAt(registeredAt)
        .updatedAt(updatedAt)
        .registerIp(registerIp)
        .updaterIp(updaterIp)
        .registeredBy(registeredBy)
        .updatedBy(updatedBy)
        .status(status)
//        .companyId(companyId)
//        .department(department)
//        .checkLogin(checkLogin)
//        .role(role)
        .build();
  }
}