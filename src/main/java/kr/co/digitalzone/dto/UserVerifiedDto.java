package kr.co.digitalzone.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import kr.co.digitalzone.entity.comm_users_verified;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserVerifiedDto {

  private String verifiedKey;
  private String userId;
  private String status;


  //comm_users_verified -> UserVerifiedDto
  public UserVerifiedDto(comm_users_verified commUsersVerified) {
    this.verifiedKey = commUsersVerified.getVerifiedKey();
    this.userId = commUsersVerified.getUserId();
    this.status = commUsersVerified.getStatus();
  }

public UserVerifiedDto toApiResponse() {
    return UserVerifiedDto.builder()
        .verifiedKey(verifiedKey)
        .userId(userId)
        .status(status)
        .build();
}

public comm_users_verified toEntity() {
    return comm_users_verified.builder()
        .verifiedKey(verifiedKey)
        .userId(userId)
        .status(status)
        .build();
}

}





