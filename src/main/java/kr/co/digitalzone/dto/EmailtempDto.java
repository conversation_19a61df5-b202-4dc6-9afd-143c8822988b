//package kr.co.digitalzone.dto;
//
//import jakarta.persistence.Id;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//public class EmailtempDto {
//    @Id
//    private String id;
//    private String title;
//    private String content;
//    private Date registeredAt;
//    private Date updatedAt;
//    private String registeredBy;
//    private String updatedBy;
//}
