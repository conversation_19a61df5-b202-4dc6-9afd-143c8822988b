package kr.co.digitalzone.util;

import java.util.Random;
import org.springframework.stereotype.Component;

@Component
public class PasswordUtils {

  /** 임시비밀번호 or OTP 생성
   * 1) input - password */
  public static String tempPassword(int size) {
    int index;
    char[] charSet = new char[] {
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'
//        ,'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
//        'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
//        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
//        'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'
    };

    StringBuffer tempPassword = new StringBuffer();
    Random random = new Random();

    for(int i = 0; i < size; i++) {
      double rd = random.nextDouble();
      index = (int)(charSet.length * rd);

      tempPassword.append(charSet[index]);
    }
    return tempPassword.toString();
  }


}
