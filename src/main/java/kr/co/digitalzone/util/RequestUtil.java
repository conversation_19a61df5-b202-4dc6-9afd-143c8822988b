package kr.co.digitalzone.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class RequestUtil {

  public static String getRemoteAddr() {
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

    String ip = request.getHeader("X-Forwarded-For");
    if (ip == null) { ip = request.getHeader("Proxy-Client-IP"); }
    if (ip == null) { ip = request.getHeader("WL-Proxy-Client-IP"); }
    if (ip == null) { ip = request.getHeader("HTTP_CLIENT_IP"); }
    if (ip == null) { ip = request.getHeader("HTTP_X_FORWARDED_FOR"); }
    if (ip == null) { ip = request.getRemoteAddr(); }

    return ip;
  }

}
