package kr.co.digitalzone.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.stereotype.Component;

@Component
public class CheckUtils {

  /** 이메일 유효성 검사
   * 1) input - email */
  public static Matcher checkEmail(String email) {
    Pattern emailPattern = Pattern.compile("^[0-9a-zA-Z]([-_\\.]?[0-9a-zA-Z])*@[0-9a-zA-Z]([-_\\.]?[0-9a-zA-Z])*\\.[a-zA-Z]{2,3}$");
    return emailPattern.matcher(email);
  }

//  /** 휴대전화 유효성 검사
//   * 1) input - phone */
//  public static Matcher checkPhone(String phone) {
//    Pattern phonePattern = Pattern.compile("(01[016789])([1-9]{1}[0-9]{2,3})([0-9]{4})$");
//    return phonePattern.matcher(phone);
//  }
}
