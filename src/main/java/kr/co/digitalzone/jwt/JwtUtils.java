package kr.co.digitalzone.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.joda.time.DateTime;
import org.joda.time.Period;

import java.security.Key;
import java.util.Date;
import java.util.Map;

public class JwtUtils {

  private static Key getKey(String secretKey) {
    return Keys.hmacShaKeyFor(Decoders.BASE64.decode(secretKey));
  } // Decoders.BASE64.decode(secretKey) : secretKey를 BASE64로 디코딩

  private static Claims getClaims(String secretKey, String jwtValue) { // Claims : JWT의 payload 부분
    return (Claims) Jwts
      .parserBuilder()
      .setSigningKey(getKey(secretKey))
      .build()
      .parse(jwtValue)
      .getBody();
  }

  public static String getUsername(String secretKey, String jwtValue) {// JWT의 payload 부분에서 username을 가져옴
    return getClaims(secretKey, jwtValue).get("email", String.class);
  }

  public static String getAttribute(String secretKey, String jwtValue, String attributeName) {// JWT의 payload 부분에서 attributeName을 가져옴
    return getClaims(secretKey, jwtValue).get(attributeName, String.class);
  }

  public static boolean isExpired(String secretKey, String jwtValue) {  // JWT의 만료일이 현재 시간보다 이전인지 확인
    return getClaims(secretKey, jwtValue).getExpiration().before(new Date());
  }

  public static String createJwt(String secretKey, int expireMinutes, String subject, Map<String, String> claimMap) { // JWT 생성
    DateTime currentDateTime = new DateTime(new Date(System.currentTimeMillis()));

    Claims claims = Jwts.claims();

    for (String key : claimMap.keySet()) {
      String value = claimMap.get(key);
      claims.put(key, value);
    }

    return Jwts
      .builder()
      .setClaims(claims)
      .setSubject(subject)
      .setExpiration(currentDateTime.plus(Period.minutes(expireMinutes)).toDate())
      .setIssuedAt(currentDateTime.toDate())
      .signWith(getKey(secretKey), SignatureAlgorithm.HS256)
      .setHeaderParam("typ", "JWT")
      .compact();
  }

  public static String createJwtUser(String secretKey, int expireMinutes, String username) {
    DateTime currentDateTime = new DateTime(new Date(System.currentTimeMillis()));

    Claims claims = Jwts.claims();
    claims.put("username", username);

    return Jwts
      .builder()
      .setClaims(claims)
      .setExpiration(currentDateTime.plus(Period.minutes(expireMinutes)).toDate())
      .setIssuedAt(currentDateTime.toDate())
      .signWith(getKey(secretKey), SignatureAlgorithm.HS256)
      .setHeaderParam("typ", "JWT")
      .compact();
  }
}
