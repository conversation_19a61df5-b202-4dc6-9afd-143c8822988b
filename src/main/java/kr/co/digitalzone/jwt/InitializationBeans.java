package kr.co.digitalzone.jwt;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.crypto.password.Pbkdf2PasswordEncoder;
import org.springframework.security.provisioning.JdbcUserDetailsManager;
import org.springframework.security.provisioning.UserDetailsManager;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
@RequiredArgsConstructor
public class InitializationBeans {

  private final DataSource dataSource;

  private final CustomUserDetailsService userDetailsService;

  @Value("${keys.password.iteration-count}")
  private int keysPasswordIterationCount;

  @Value("${keys.password.salt-length}")
  private int keysPasswordSaltLength;

  @Value("${keys.password.secret}")
  private String keysPasswordSecret;

  @Bean
  public UserDetailsManager users(HttpSecurity http) throws Exception {
    AuthenticationManager authenticationManager = http.getSharedObject(AuthenticationManagerBuilder.class)
      .userDetailsService(userDetailsService)
      .passwordEncoder(passwordEncoder())
      .and()
      .authenticationProvider(authenticationProvider())
      .build();

    JdbcUserDetailsManager jdbcUserDetailsManager = new JdbcUserDetailsManager(dataSource);
    jdbcUserDetailsManager.setAuthenticationManager(authenticationManager);
    return jdbcUserDetailsManager;
  }

  @Bean
  public DaoAuthenticationProvider authenticationProvider() {
    final DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
    authProvider.setUserDetailsService(userDetailsService);
    authProvider.setPasswordEncoder(passwordEncoder());
    return authProvider;
  }

  @Bean
  public Pbkdf2PasswordEncoder passwordEncoder() {
    // 256 byte
    return new Pbkdf2PasswordEncoder(keysPasswordSecret, keysPasswordSaltLength, keysPasswordIterationCount, Pbkdf2PasswordEncoder.SecretKeyFactoryAlgorithm.PBKDF2WithHmacSHA512);
  }

}
