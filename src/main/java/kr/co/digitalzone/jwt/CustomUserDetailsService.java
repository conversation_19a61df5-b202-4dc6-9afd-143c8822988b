package kr.co.digitalzone.jwt;

import kr.co.digitalzone.entity.comm_users;
import kr.co.digitalzone.member.repository.MembersRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomUserDetailsService implements UserDetailsService {

  private final MembersRepository userRepository;

  @Override
  public UserDetails loadUserByUsername(String username) {
    comm_users appUser = userRepository.findByEmail(username);

    if (appUser == null) {
      throw new UsernameNotFoundException(username);
    }

    return new CustomUserPrincipal(appUser);
  }

}