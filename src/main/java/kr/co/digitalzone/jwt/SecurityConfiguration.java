package kr.co.digitalzone.jwt;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.servlet.PathRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.data.repository.query.SecurityEvaluationContextExtension;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;

import java.util.Collections;
import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfiguration {

  private final Environment env;
  private final CustomUserDetailsService userService;

  private final CustomAccessDeniedHandler customAccessDeniedHandler;
  private final CustomAuthenticationEntryPoint customAuthenticationEntryPoint;

  @Value("${keys.security.access.secret}")
  private String keysSecurityAccessSecret;

  @Autowired
  private AuthenticationConfiguration authenticationConfiguration;

  @Bean
  public SecurityEvaluationContextExtension securityEvaluationContextExtension() {
    return new SecurityEvaluationContextExtension();
  }

  @Bean
  public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    http
      .headers(headers -> headers.frameOptions().disable());

    http
      .httpBasic().disable(); // Basic 인증 비활성화

    http
      .cors().configurationSource(request -> { // CORS 설정
        CorsConfiguration config = new CorsConfiguration(); // CORS 설정
//        config.setAllowedOrigins(Collections.singletonList("*"));
        config.setAllowedOriginPatterns(Collections.singletonList("*"));
        config.setAllowedMethods(Collections.singletonList("*"));
        config.setAllowedHeaders(Collections.singletonList("*"));
        config.setMaxAge(3600L);
        config.setAllowCredentials(true);
        config.setExposedHeaders(List.of(HttpHeaders.AUTHORIZATION));
        return config;
      });

    http
      .csrf().disable(); // CSRF 비활성화

    http //
      .authorizeHttpRequests(auth -> {
//        auth.requestMatchers("/login/**").permitAll();

        auth.requestMatchers("/thumbnail/**").permitAll(); // 썸네일 이미지
        auth.requestMatchers("/user/");
        auth.requestMatchers("/upload/**").permitAll(); // 업로드 이미지
        auth.requestMatchers("/download/**").permitAll(); // 다운로드 이미지
        auth.requestMatchers("/worked/**").permitAll(); // 공간 이미지
        auth.requestMatchers("/admin/**").permitAll(); // 관리자 페이지
          auth.requestMatchers("/admin/swagger-ui", "/swagger-ui.html", "/v3/api-docs/**", "/swagger-ui/**").permitAll();
          auth.requestMatchers("/login/**").permitAll();
          auth.requestMatchers("/crawling/**").permitAll();
          auth.requestMatchers("/crawling/**").permitAll();
          auth.requestMatchers("/crawling/keywordCount").permitAll();
          auth.anyRequest().authenticated();


      });

    // auth.requestMatchers("/admin/**").hasRole("ADMIN");
//    http
//      .authorizeHttpRequests(auth -> {
//          auth.requestMatchers("/**").permitAll(); // 모든 요청에 대해 인증 절차 없이 허용
//      });

    http // 로그인 설정
      .formLogin()
      .usernameParameter("email")
      .passwordParameter("password");

    http // 세션 설정
      .sessionManagement()
      .sessionCreationPolicy(SessionCreationPolicy.STATELESS);

    http
      .addFilter(new CustomUsernamePasswordAuthenticationFilter(authenticationConfiguration.getAuthenticationManager(), userService, env))
      .addFilterBefore(new JwtFilter(keysSecurityAccessSecret), CustomUsernamePasswordAuthenticationFilter.class);

    http
      .exceptionHandling()
      .accessDeniedHandler(customAccessDeniedHandler)
      .authenticationEntryPoint(customAuthenticationEntryPoint);

    return http.build();
  }

  @Bean
  public WebSecurityCustomizer webSecurityCustomizer() {
    return (web) -> web.ignoring()
      .requestMatchers(PathRequest.toStaticResources().atCommonLocations())
      .requestMatchers("/assets/**")
      .requestMatchers("/resources/**");
  }

  @Bean
  AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
    return authenticationConfiguration.getAuthenticationManager();
  }

}
