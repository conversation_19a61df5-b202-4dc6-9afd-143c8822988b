package kr.co.digitalzone.jwt;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
public class JwtValidatorFilter extends OncePerRequestFilter {

  @Value("${keys.security.access.secret}")
  private final String keysSecurityAccessSecret;

  @Override
  public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
    throws IOException, ServletException {
    String token = request.getHeader(HttpHeaders.AUTHORIZATION);

    if (null != token) {
      try {
        String userName = JwtUtils.getAttribute(keysSecurityAccessSecret, token, "username");
        String authorities = JwtUtils.getAttribute(keysSecurityAccessSecret, token, "authorities");

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userName, null, List.of(new SimpleGrantedAuthority("USER")));

        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
      } catch (Exception e) {
        throw new BadCredentialsException("Invalid Token received!");
      }
    }

    chain.doFilter(request, response);
  }

  @Override
  protected boolean shouldNotFilter(HttpServletRequest request) {
    return request.getServletPath().equals("/user/signin") ||
      request.getServletPath().equals("/user/signup") ||
      request.getServletPath().equals("/user/signup/admin");
  }

}
