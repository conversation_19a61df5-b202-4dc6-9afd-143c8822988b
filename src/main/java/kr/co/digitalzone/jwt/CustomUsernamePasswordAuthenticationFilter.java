package kr.co.digitalzone.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kr.co.digitalzone.dto.MembersDto;
import kr.co.digitalzone.member.service.MembersService;
import kr.co.digitalzone.vo.RequestLogin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class CustomUsernamePasswordAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

  private final CustomUserDetailsService userService;
  private final Environment env;

  public CustomUsernamePasswordAuthenticationFilter(AuthenticationManager authenticationManager,
                                                    CustomUserDetailsService userService,
                                                    Environment env) {
    super.setAuthenticationManager(authenticationManager);
    this.userService = userService;
    this.env = env;
  }

  @Override
  public Authentication attemptAuthentication(HttpServletRequest request,
                                              HttpServletResponse response) throws AuthenticationException {
    try {
      RequestLogin creds = new ObjectMapper().readValue(request.getInputStream(), RequestLogin.class);

      return getAuthenticationManager().authenticate(
              new UsernamePasswordAuthenticationToken(
                      creds.getEmail(),
                      creds.getPassword(),
                      new ArrayList<>()
              )
      );
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  protected void successfulAuthentication(HttpServletRequest request,
                                          HttpServletResponse response,
                                          FilterChain chain,
                                          Authentication authResult) {
    String userName = ((CustomUserPrincipal) authResult.getPrincipal()).getUsername();

    CustomUserPrincipal userDetails = (CustomUserPrincipal) userService.loadUserByUsername(userName);

    Map<String, String> tokenData = new HashMap<>();
    tokenData.put("email", userDetails.getUsername());
    
    // TODO : 토큰시간 수정 필요
    String jwtAccess = JwtUtils.createJwt(env.getProperty("keys.security.access.secret"), 1440, userDetails.getUsername(), tokenData);
//    String jwtAccess = JwtUtils.createJwt(env.getProperty("keys.security.access.secret"), 10, userDetails.getEmail(), tokenData);

    response.addHeader("jwtAccess", jwtAccess);
    response.addHeader("email", userDetails.getUsername());

    // TODO : 리팩토링 필요
    // 클라이언트 측 AXIOS에서 Header를 추출하지 못하여 응답 데이터 추가

    ObjectMapper om =  new ObjectMapper();
    ObjectNode on =  om.createObjectNode();

    on.put("jwtAccess", jwtAccess);
    on.put("email", userDetails.getUsername());

    PrintWriter writer = null;
    try {
      writer = response.getWriter();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    writer.print(on.toString());
  }

}