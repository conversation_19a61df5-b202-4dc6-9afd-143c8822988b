package kr.co.digitalzone.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kr.co.digitalzone.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.PrintWriter;

@Component
@Slf4j
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

  @Override
  public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) throws IOException, ServletException {
    log.info("#### request : {}", request.getRequestURI().toString());
    log.error("Unauthorized error: {}", authException.getMessage()); // 상세 로그 추가

    ApiResponse apiResponse = new ApiResponse<>();
    apiResponse.setCode(401);
    apiResponse.setMessage("Unauthorized");

    ObjectMapper objectMapper = new ObjectMapper();
    String json = objectMapper.writeValueAsString(apiResponse);

    response.setContentType(MediaType.APPLICATION_JSON_VALUE);
    response.setStatus(HttpStatus.UNAUTHORIZED.value());
    PrintWriter writer = response.getWriter();
    writer.write(json);
    writer.flush();
  }

}
