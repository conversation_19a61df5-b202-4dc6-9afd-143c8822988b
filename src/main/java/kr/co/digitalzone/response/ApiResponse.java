package kr.co.digitalzone.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {

  private int code;
  private String message;
  private T data;

  public static <T> ApiResponse<T> res(int code, String message, T data) {
    return new ApiResponse<>(code, message, data);
  }

  public static ApiResponse res(int code, String message) {
    return new ApiResponse(code, message, null);
  }

  public static ApiResponse res(ApiResponseEnum apiResponseEnum) {
    return new ApiResponse(apiResponseEnum.getCode(), apiResponseEnum.getMessage(), null);
  }
  public static <T> ApiResponse<T> res(ApiResponseEnum apiResponseEnum, T data) {
    return new ApiResponse(apiResponseEnum.getCode(), apiResponseEnum.getMessage(), data);
  }
  public static ApiResponse ok() {
    return ApiResponse.res(ApiResponseEnum.OK, null);
  }
  public static <T> ApiResponse<T> ok(T data) {
    return ApiResponse.res(ApiResponseEnum.OK, data);
  }

}
