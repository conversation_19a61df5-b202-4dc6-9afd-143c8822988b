package kr.co.digitalzone.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ApiResponseEnum {
  OK (200, "정상 처리 되었습니다"),
  CREATED (201, " 생성 되었습니다"),
//  NO (400, "서버의 요청을 이해할 수 없습니다"),
  NO_REQUEST(401, "요청을 실행할 수 없습니다"),
//  NOT_FOUND (404, "리소스를 찾을 수 없습니다"),
//  UNAUTHORIZED (402, "권한이 없습니다"),
  DUPLICATE_EMAIL (10009, "중복된 이메일입니다"),
  DUPLICATE_CLASSNAME (10009, "중복된 클래스명입니다"),
  DUPLICATE_PROJECTNAME (10009, "중복된 프로젝트명입니다"),
  DUPLICATE_FILENAME (10009, "중복된 파일명입니다"),
  INVALID_EMAIL (10009, "유효하지 않은 이메일입니다"),
  INVALID_PHONE (10009, "유효하지 않은 전화번호입니다"),
  NO_SEND_EMAIL(10009, "이메일 전송을 실패하였습니다"),
  NON_EXISTS_EMAIL(10009, "존재하지 않는 이메일입니다"),
  NON_EXISTS_MEMBER(10009, "존재하지 않는 회원입니다"),
  FAIL_AUTHENTICATION(10009, "이메일 인증실패"),
  NON_EXISTS_PHONE(10009, "저장되어 있지 않은 번호입니다"),
  NON_EXISTS_PASSWORD(10009, "비밀번호를 확인해주세요"),
  UNABLE_TO_PERFORM(10009, "처리 불가능한 요청입니다"),
  TIMEOUT_EXPIRED(10009, "제한 시간이 완료되었습니다"),
  NO_SEND_INVITE_EMAIL(10009, "로그인 이력이 있는 회원에게 초대 메일을 보낼 수 없습니다"),
  PREVIOUS_PASSWORD(10009, "이전 비밀번호는 사용할 수 없습니다")
  ,INVALID_ACCOUNT (10009, "인증번호가 일치하지 않습니다."),
  UNVERIFIED_EMAIL(10009, "올바른 이메일이 아닙니다."),
  MISSING_REQUEST_HEADER(10012, "필수 요청 헤더가 누락되었습니다."),
    EXPIRED_PROJECT(20009, "만료된 프로젝트를 수정할 수 없습니다.");

  private int code;
  private String message;
}
