package kr.co.digitalzone.user.jobItem.repository;

import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserAnnotationsRespDto;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserClassInfoByPjcRespDto;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserFileInfoByfidRepsDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static kr.co.digitalzone.user.jobItem.domain.QJobLabel.jobLabel;
import static kr.co.digitalzone.user.classItem.domain.QClassItem.classItem;
import static kr.co.digitalzone.user.classItem.domain.QProjectClass.projectClass;
import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.*;
import static kr.co.digitalzone.user.project.domain.QProject.project;

@Repository
@RequiredArgsConstructor
public class UserBlurLablelJobQRepository {
    private final EntityManager em;

    /* 프로젝트번호별 클래스정보 */
    public List<UserClassInfoByPjcRespDto> getClassInfo(Long projectId) {;
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(
                        new QUserJobItemRespDto_UserClassInfoByPjcRespDto(
                                classItem.classId,
                                classItem.className,
                                classItem.classColor,
                                classItem.classType
//                                new CaseBuilder()
//                                        .when(classItem.classType.eq("B")).then("Bounding Box")
//                                        .when(classItem.classType.eq("P")).then("Polygon")
//                                        .otherwise("Key Point")
                                       ))
                .from(projectClass)
                .join(projectClass.classItem)
                .where(projectClass.project.projectId.eq(projectId))
                .fetch();
    }

    /* 파일정보 */
    public UserFileInfoByfidRepsDto getFileInfo(Long fileId){
        JPAQueryFactory query = new JPAQueryFactory(em);
      return  query.select(new QUserJobItemRespDto_UserFileInfoByfidRepsDto(
                              projectFile.fileId,
                              projectFile.fileUrl,
                              project.projectName,
                              projectFile.fileName,
                              project.endAt
                        )
                )
                .from(project)
                .join(project.projectFile, projectFile)
                .where(projectFile.fileId.eq(fileId))
//                .join(jobItem.project, project)
                  .fetchOne();

    }

    /* 파일별 라벨링정보 */
    public List<UserAnnotationsRespDto> getAnnotatationInfo(Long fileId) {

        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(new QUserJobItemRespDto_UserAnnotationsRespDto(
                           jobLabel.labelId,
                           jobLabel.projectFile.fileId,
                           jobLabel.classItem.classId,
                           jobLabel.labelType,
//                           jobLabel.labelArea,
                           jobLabel.labelBbox,
                            classItem.classColor

                        )
                )
                .from(jobLabel)
                .join(jobLabel.classItem, classItem)
                .where(jobLabel.projectFile.fileId.eq(fileId), jobLabel.delYn.eq("N"))
                .fetch();
    }
}