package kr.co.digitalzone.user.jobItem.repository;

import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserApproveResponse;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserTabListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;
import com.querydsl.core.types.OrderSpecifier;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.UserApproveResponse;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.UserTabListResponse;
import static kr.co.digitalzone.user.project.domain.QProject.project;
import static org.hibernate.query.results.Builders.fetch;

@Repository
@RequiredArgsConstructor
public class UserBlurJobQueryRepository {
    private final EntityManager em;

    /* 비식별화 작업목록 조회 */
    public Page<UserTabListResponse> findBlurJob(Long projectId, Pageable pageable, String keyword) {
        JPAQueryFactory query = new JPAQueryFactory(em);

        List<UserTabListResponse> content = query.select(
                new QUserJobItemRespDto_UserTabListResponse(
                          projectFile.fileId
                        , projectFile.fileName
                        , new CaseBuilder()
                                .when(projectFile.fileType.eq("I")).then("이미지")
                                .when(projectFile.fileType.eq("V")).then("동영상")
                                .otherwise("텍스트")
                        , projectFile.updateAt
                        , projectFile.updatedBy
//                        , new CaseBuilder()
//                                .when(projectFile.fileBluring.eq("Y")).then("완료")
//                                .otherwise("진행")
                        ,projectFile.fileUrl
                        )
                )
                .from(projectFile)
                .innerJoin(project)
                .on(project.projectId.eq(projectFile.project.projectId))
                .where(
                          projectFile.delYn.eq("N")
                        , projectFile.project.projectId.eq(projectId)
                        , projectFile.fileName.contains(keyword)
                        , projectFile.fileBluring.eq("N")
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(projectFile.updateAt.asc())
                .fetch();

        JPAQuery<Long> countQuery = query
                .select(projectFile.fileId.count())
                .from(projectFile)
                .where(
                          projectFile.delYn.eq("N")
                        , projectFile.project.projectId.eq(projectId)
                        , projectFile.fileName.contains(keyword)
                        , project.blurYn.eq("Y") // 추가된 조건

                );

        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
    }

    /* 라벨링 작업목록 조회 */
    public Page<UserTabListResponse> findLabelJob(Long jobId, Pageable pageable, String keyword) {
        JPAQueryFactory query = new JPAQueryFactory(em);

        List<UserTabListResponse> content = query.select(
                        new QUserJobItemRespDto_UserTabListResponse(
                                  projectFile.fileId
                                , projectFile.fileName
                                , new CaseBuilder()
                                .when(projectFile.fileType.eq("I")).then("이미지")
                                .when(projectFile.fileType.eq("V")).then("동영상")
                                .otherwise("텍스트")
                                , projectFile.updateAt
                                , projectFile.updatedBy
//                                , new CaseBuilder()
//                                .when(projectFile.fileLabeling.eq("Y")).then("완료")
//                                .otherwise("진행")
                                ,projectFile.fileUrl
                        )
                )
                .from(projectFile)
                .innerJoin(project)
                .on(project.projectId.eq(projectFile.project.projectId))
                .where(
                          projectFile.delYn.eq("N")
                        , projectFile.project.projectId.eq(jobId)
                        , projectFile.fileName.contains(keyword)
                        , projectFile.fileBluring.eq("Y")
                        , projectFile.fileLabeling.eq("N")
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(
                        new OrderSpecifier<>(
                                Order.ASC,
                                new CaseBuilder()
                                        .when(project.blurYn.eq("N")).then(projectFile.updateAt)
                                        .otherwise(Expressions.nullExpression())
                        ),
                        new OrderSpecifier<>(
                                Order.DESC,
                                new CaseBuilder()
                                        .when(project.blurYn.eq("Y")).then(projectFile.updateAt)
                                        .otherwise(Expressions.nullExpression())
                        )
                )
                .fetch();


        JPAQuery<Long> countQuery = query
                .select(projectFile.fileId.count())
                .from(projectFile)
                .where(
                          projectFile.delYn.eq("N")
//                        , projectFile.ccode.eq(ccode)
                        , projectFile.project.projectId.eq(jobId)
                        , projectFile.fileName.contains(keyword)
                );

        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
    }

    /* 완료내역 작업목록 조회 */
    public Page<UserTabListResponse> findCompleteJob(Long jobId, Pageable pageable, String keyword) {
        JPAQueryFactory query = new JPAQueryFactory(em);

        List<UserTabListResponse> content = query.select(
                        new QUserJobItemRespDto_UserTabListResponse(
                                  projectFile.fileId
                                , projectFile.fileName
                                , new CaseBuilder()
                                .when(projectFile.fileType.eq("I")).then("이미지")
                                .when(projectFile.fileType.eq("V")).then("동영상")
                                .otherwise("텍스트")
                                , projectFile.updateAt
                                , projectFile.updatedBy
//                                , new CaseBuilder()
//                                .when(projectFile.fileStatus.eq("E")).then("작업완료")
//                                .otherwise("진행")
                                ,projectFile.fileUrl
                        )
                )
                .from(projectFile)
                .innerJoin(project)
                .on(project.projectId.eq(projectFile.project.projectId))
                .where(
                          projectFile.delYn.eq("N")
//                        , projectFile.ccode.eq(ccode)
                        , projectFile.project.projectId.eq(jobId)
                        , projectFile.fileName.contains(keyword)
                        , projectFile.fileBluring.eq("Y")
                        , projectFile.fileLabeling.eq("Y")
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(projectFile.updateAt.desc())
                .fetch();

        JPAQuery<Long> countQuery = query
                .select(projectFile.fileId.count())
                .from(projectFile)
                .where(
                          projectFile.delYn.eq("N")
//                        , projectFile.ccode.eq(ccode)
                        , projectFile.project.projectId.eq(jobId)
                        , projectFile.fileBluring.eq("Y")
                        , projectFile.fileLabeling.eq("Y")
                        , projectFile.fileName.contains(keyword)
                );

        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
    }

//    /* 검수완료 작업목록 조회 */
//    public Page<UserApproveResponse> findApproveJob(String email, Long jobId, Pageable pageable, String keyword) {
//        JPAQueryFactory query = new JPAQueryFactory(em);
//
//        List<UserApproveResponse> content = query.select(
//                        new QUserJobItemRespDto_UserApproveResponse(
//                                  projectFile.fileId
//                                , projectFile.fileName
//                                , new CaseBuilder()
//                                        .when(projectFile.fileType.eq("I")).then("이미지")
//                                        .when(projectFile.fileType.eq("V")).then("동영상")
//                                        .otherwise("텍스트")
//                                , projectFile.updateAt
//                                , projectFile.updatedBy
//                                , new CaseBuilder()
//                                    .when(projectFile.fileStatus.eq("V")).then("검수완료")
//                                    .otherwise("진행")
//                                , projectFile.fileUrl)
//
//                )
//                .from(projectFile)
//                .innerJoin(project)
//                .on(project.projectId.eq(projectFile.project.projectId))
//                .where(
//                          projectFile.delYn.eq("N")
////                        , projectFile.ccode.eq(ccode)
//                        , projectFile.project.projectId.eq(jobId)
//                        , projectFile.fileLabeling.eq("Y")
//                        , projectFile.fileStatus.eq("V")
//                        , projectFile.fileName.contains(keyword)
//                )
//                .offset(pageable.getOffset())
//                .limit(pageable.getPageSize())
//                .orderBy(projectFile.fileId.desc())
//                .fetch();
//
//        JPAQuery<Long> countQuery = query
//                .select(projectFile.fileId.count())
//                .from(projectFile)
//                .where(
//                          projectFile.delYn.eq("N")
////                        , projectFile.ccode.eq(ccode)
//                        , projectFile.project.projectId.eq(jobId)
//                        , projectFile.fileLabeling.eq("Y")
//                        , projectFile.fileStatus.eq("V")
//                        , projectFile.fileName.contains(keyword)
//                );
//
//        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
//    }
}