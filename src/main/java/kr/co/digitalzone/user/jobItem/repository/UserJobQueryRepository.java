package kr.co.digitalzone.user.jobItem.repository;

import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserJobBlurResponse;
import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserJobProgressResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.*;


@Repository
@RequiredArgsConstructor
public class UserJobQueryRepository {
    private final EntityManager em;

    /* 작업목록 내역 조회 */
//    public Page<UserJobListResponse> findJobList(String ccode, String email, Pageable pageable) {
//        JPAQueryFactory query = new JPAQueryFactory(em);
//
//        List<UserJobListResponse> content = query.selectDistinct(
//                        new QUserJobItemRespDto_UserJobListResponse(
//                                jobItem.project.projectId,
//                                project.projectName,
//                                jobItem.jobId,
//                                jobItem.jobName,
//                                project.registeredAt,
//                                project.endAt,
//                                ExpressionUtils.as(
//                                        JPAExpressions.select(projectFile.fileId.count())
//                                            .from(projectFile)
//                                            .where(
//                                                    projectFile.project.projectId.eq(project.projectId),
//                                                    projectFile.ccode.eq(ccode),
//                                                    projectFile.delYn.eq("N")
//                                            ), "fileCount"),
//                                new CaseBuilder()
//                                        .when(jobItem.jobStatus.eq("P")).then("진행중")
//                                        .when(jobItem.jobStatus.eq("E")).then("검수중")
//                                        .otherwise("검수완료")
//                        )
//                )
//                .from(jobItem)
//                .innerJoin(project)
//                .on(project.projectId.eq(jobItem.project.projectId))
//                .innerJoin(jobUser)
//                .on(jobUser.jobItem.jobId.eq(jobItem.jobId))
//                .innerJoin(comm_users)
//                .on(comm_users.email.eq(jobUser.userEmail))
//                .where(
//                        jobItem.ccode.eq(ccode),
//                        jobItem.delYn.eq("N"),
//                        jobUser.userEmail.eq(email)
//                )
//                .offset(pageable.getOffset())
//                .limit(pageable.getPageSize())
//                .fetch();
//
//        JPAQuery<Long> countQuery = query
//                .select(jobItem.jobId.count())
//                .from(jobItem)
//                .innerJoin(project)
//                .on(project.projectId.eq(jobItem.project.projectId))
//                .innerJoin(jobUser)
//                .on(jobUser.jobItem.jobId.eq(jobItem.jobId))
//                .innerJoin(comm_users)
//                .on(comm_users.email.eq(jobUser.userEmail))
//                .where(
//                        jobItem.ccode.eq(ccode),
//                        jobItem.delYn.eq("N"),
//                        jobUser.userEmail.eq(email)
//                );
//
//        List<UserJobListResponse> collect = content.stream().map(item -> {
//            UserJobBlurResponse userJobBlurResponse = blurCount(ccode, item.getProjectId());
//            UserJobProgressResponse userJobProgressResponse = progressCount(ccode, item.getProjectId());
//
//            setUserBlur(userJobBlurResponse, item);
//
//            setUserProgress(userJobProgressResponse, item);
//
//            return item;
//        }).collect(Collectors.toList());
//
//        return PageableExecutionUtils.getPage(collect, pageable, countQuery::fetchCount);
//    }

    // 비식별화,라벨링 수
    private void setUserBlur(UserJobBlurResponse data, UserJobListResponse item) {
        Integer blurCount = data.getBlurCount();
        if (!StringUtils.isEmpty(blurCount)) {
            item.setBlurCount(Long.valueOf(blurCount));
        } else {
            item.setBlurCount(0L);
        }

        Integer labelCount = data.getLabelCount();
        if (!StringUtils.isEmpty(labelCount)) {
            item.setLabelCount(Long.valueOf(labelCount));
        } else {
            item.setLabelCount(0L);
        }
    }

    private void setUserProgress(UserJobProgressResponse data, UserJobListResponse item) {
        Long fileCount = data.getFileCount();
        Long fileTotalCount = data.getFileTotalCount();

        if (fileTotalCount > 0 && fileCount > 0) {
            Double progress = ((double) fileCount / (double) fileTotalCount) * 100.0;
            item.setProgress(String.format("%d", Math.round(progress)));
        } else {
            item.setProgress("0");
        }
    }

    /* 비식별화, 라벨수 */
    public UserJobBlurResponse blurCount(Long projectId) {
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(
                        new QUserJobItemRespDto_UserJobBlurResponse(
                                new CaseBuilder()
                                        .when(projectFile.fileBluring.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum() ,
                                new CaseBuilder()
                                        .when(projectFile.fileLabeling.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum()
                        )
                )
                .from(projectFile)
                .where(
//                        projectFile.ccode.eq(ccode) ,
                        projectFile.delYn.eq("N") ,
                        projectFile.project.projectId.eq(projectId)
                )
                .fetchOne();
    }

}