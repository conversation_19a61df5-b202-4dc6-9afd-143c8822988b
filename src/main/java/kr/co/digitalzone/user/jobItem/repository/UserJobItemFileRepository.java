package kr.co.digitalzone.user.jobItem.repository;

import kr.co.digitalzone.user.file.domain.ProjectFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserJobItemFileRepository extends JpaRepository<ProjectFile, Long> {

    /* 비식별화 초기화 */
    @Modifying(clearAutomatically = true)
    @Query("update AIL_PROJECT_FILE f " +
            "set f.fileUrl = replace(f.fileUrl,'.dat','')," +
            "f.fileReal = replace(f.fileReal,'.dat',''), "+
            "f.fileStatus = 'P', " +
            "f.fileBluring = 'N', " +
            "f.fileLabeling='N' " +
            "where f.fileId in (:fileId)")
    void blurSttusReset(@Param("fileId") List<Long> jobIdList);

    /*비식별화 미사용시 초기화*/
    @Modifying
    @Query("update AIL_PROJECT_FILE f " +
            "set " +
            "f.fileStatus = 'P', " +
            "f.fileLabeling='N' " +
            "where f.fileId in (:fileId)")
    public void labelSttusReset(@Param("fileId") List<Long> jobIdList);


    /* 비식별화 저장 */
//    @Modifying(clearAutomatically = true)
//    @Query("update AIL_PROJECT_FILE f " +
//            "   set f.fileUrl = replace (f.fileUrl , f.fileExt ,'dat.' || f.fileExt), " +
//            "       f.fileReal = replace (f.fileReal, f.fileReal ,f.fileReal || '.dat'), " +
//            "       f .fileBluring = 'Y' " +
//            " where f.fileId  = :fileId ")
//    void saveBlurJob(@Param("fileId") long fileId);

    /* 라벨링 저장 */
//    @Modifying(clearAutomatically = true)
//    @Query("update AIL_PROJECT_FILE f " +
//            "   set f.fileLabeling = 'Y' " +
//            " where f.fileId  = :fileId ")
//    void saveLabelJob(@Param("fileId") long fileId);

    @Modifying(clearAutomatically = true)
    @Query("update AIL_PROJECT_FILE f " +
            "   set f.fileStatus = 'E' " +
            " where f.fileId  = :fileId ")
    void updateFileSttus(@Param("fileId") long fileId);
}

