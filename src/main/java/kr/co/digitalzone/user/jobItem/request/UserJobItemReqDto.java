package kr.co.digitalzone.user.jobItem.request;

import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class UserJobItemReqDto {

    // 비식별화 초기화
    @Getter @Setter
    public static class BlurResetRequest {
        private Long fileId;
    }

    // 비식별화 , 라벨링 정보조회
    @Getter @Setter
    public static class UserBlurLabelReqDto {
        private Long fileId;
        private Long projectId;
    }

    @Getter @Setter
    public static class UserAnnotatoinReqDto {
        private List<UserAnnotationsReqDto> annotations;
    }

    @Getter @Setter
    public static class UserAnnotationsReqDto {
        private Long labelId;
        private Long fileId;
        private Long classId;
        private String labelType;
//        private String labelArea;
        private String labelBbox;
    }
}