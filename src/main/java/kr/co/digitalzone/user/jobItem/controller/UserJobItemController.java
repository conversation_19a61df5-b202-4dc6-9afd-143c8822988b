package kr.co.digitalzone.user.jobItem.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import kr.co.digitalzone.jwt.CustomUserDetailsService;
import kr.co.digitalzone.response.ApiResponse;
import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.user.common.dto.ResponsePageDto;
import kr.co.digitalzone.user.common.request.SearchRequest;
import kr.co.digitalzone.user.file.service.UserFileService;
import kr.co.digitalzone.user.jobItem.service.UserJobItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static kr.co.digitalzone.user.jobItem.request.UserJobItemReqDto.UserAnnotatoinReqDto;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.*;

@Validated
@Tag(name = "작업 관련 API (비식별화,라벨링)", description = "프로젝트 생성 후, 작업할 프로젝트 내에 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserJobItemController {

    private final UserJobItemService userJobItemService;
    private final CustomUserDetailsService customUserDetailsService;
    private final UserFileService userFileService;

    // 파일 다운로드 경로
    @Value("${spring.file.download.path}")
    String downloadPath;

    @Value("${spring.file.upload.path}")
    String uploadPath;

    // 현재 시간 - 파일명 활용
    LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Seoul"));
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    String nowTime = now.format(formatter);

    /* 작업목록 내역 조회 */
//    @GetMapping("/findJob")
//    public ResponseEntity<?> findJob(@RequestHeader(value = "ccode") String ccode, @RequestHeader(value = "email") String email,
//                                     @PageableDefault(page = 0, size = 10, sort = "id", direction = Sort.Direction.DESC) Pageable pageable) {
//        Authentication authentication = SecurityContextHolder.getContext().getAuthentication(); // Token 안에 있는 email 가져오기
//        Page<UserJobListResponse> result = userJobItemService.findJobList(ccode, email, pageable);
//        return new ResponseEntity<>(new ResponsePageDto<>(200, "작업목록 내역 조회 성공", result), HttpStatus.OK);
//    }


    /* 비식별화 작업목록 조회 */
    @GetMapping("/findBlurJob")
    @Operation(summary= "비식별화 작업목록 조회", description = "프로젝트 생성 후, 작업할 프로젝트 클릭 시 해당 프로젝트 작업내용 조회")
    public ResponseEntity<?> findBlurJob(@RequestParam("projectId") Long projectId,
                                         @PageableDefault(page = 0, size = 6, sort = "id", direction = Sort.Direction.ASC) Pageable pageable,
                                         SearchRequest searchRequest) {
        Page<UserTabListResponse> result = userJobItemService.findBlurJob( projectId, pageable, searchRequest);
        return new ResponseEntity<>(new ResponsePageDto<>(200, "비식별화 목록 조회 성공", result), HttpStatus.OK);
    }

    /* 라벨링 작업목록 조회 */
    @GetMapping("/findLabelJob")
    @Operation(summary="라벨링 작업목록 조회", description = "프로젝트 내, 비식별화 완료된 작업목록이 데이터라벨링 작업목록으로 이동")
    public ResponseEntity<?> findLabelJob(@RequestParam("projectId") Long projectId,
                                          @PageableDefault(page = 0, size = 6, sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
                                          SearchRequest searchRequest) {
        Page<UserTabListResponse> result = userJobItemService.findLabelJob(projectId, pageable, searchRequest);

        return new ResponseEntity<>(new ResponsePageDto<>(200, "라벨링 작업목록 조회 성공", result), HttpStatus.OK);
    }

    /* 완료내역 작업목록 조회 */
    @GetMapping("/findCompleteJob")
    @Operation(summary="완료내역 작업목록 조회", description = "프로젝트 내, 데이터라벨링까지 완료된 작업목록 조회")
    public ResponseEntity<?> findCompleteJob(@RequestParam("projectId") Long projectId,
                                             @PageableDefault(page = 0, size = 6, sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
                                             SearchRequest searchRequest) {
        Page<UserTabListResponse> result = userJobItemService.findCompleteJob(projectId, pageable, searchRequest);
        return new ResponseEntity<>(new ResponsePageDto<>(200, "완료 작업목록 조회 성공", result), HttpStatus.OK);
    }

    /* 비식별화 작업요청 */
    @GetMapping("/loadBlur")
    @Operation(summary= "비식별화 작업요청", description = "비식별화 작업목록에서 파일 클릭 시 비식별화 작업화면에 이미지 불러옴")
    public ResponseEntity<?> getBlurHistory(@RequestParam("fileId") Long fileId
    ) {

        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();
        UserFileInfoByfidRepsDto blurInfo = null;
        try {
            blurInfo = userJobItemService.getBlurHistory(fileId);
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getMessage().equals(ApiResponseEnum.EXPIRED_PROJECT.getMessage())) {
                code = ApiResponseEnum.EXPIRED_PROJECT.getCode();
                message = ApiResponseEnum.EXPIRED_PROJECT.getMessage();
            }
            return new ResponseEntity<>(ApiResponse.res(code, message), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(new ResponseDto<>(code, "비식별화 작업요청 성공", blurInfo), HttpStatus.OK);
    }

    /* 비식별화 작업저장 */
    @PostMapping("/saveBlur")
    @Operation(summary="비식별화 작업 저장", description = "비식별화 작업 후 임시저장의 개념으로 저장되며, 쿼리에는 변화 없음")
    public ResponseEntity<?> saveBlurJob(@RequestPart("fileId") Long fileId,
                                         @RequestPart(value = "file", required = false) MultipartFile file) {
        userJobItemService.saveBlurJob(fileId, file);

        return new ResponseEntity<>(new ResponseDto<>(200, "비식별화 작업저장 성공", null), HttpStatus.OK);
    }

    /* 비식별화 작업완료 */
    @PostMapping("/completeBlur")
    @Operation(summary="비식별화 작업 완료", description = "비식별화 작업 후 비식별화된 이미지가 서버에 저장")
    public ResponseEntity<?> completeBlurJob(@RequestPart("fileId") Long fileId,
                                             @RequestPart(value = "file", required = false) MultipartFile file) {
        userJobItemService.completeBlurJob(fileId, file);

        return new ResponseEntity<>(new ResponseDto<>(200, "비식별화 작업완료 성공", null), HttpStatus.OK);
    }


    /* 라벨링작업요청 */
    @GetMapping("/loadLabeling")
    @Operation(summary= "라벨링 작업 요청", description = "라벨링  작업목록에서 파일 클릭 시 라벨링 작업화면에 이미지 불러옴")
    public ResponseEntity<?> getLabelingHistory(@RequestParam("projectId") Long projectId, @RequestParam("fileId") Long fileId
    ) {
        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();
        UserLabelHIstoryRespDto labelHIstoryDto = null;
        try {
            labelHIstoryDto = userJobItemService.loadLabelingJob(projectId, fileId);
        } catch (Exception e) {
            e.printStackTrace();
            if (e.getMessage().equals(ApiResponseEnum.EXPIRED_PROJECT.getMessage())) {
                code = ApiResponseEnum.EXPIRED_PROJECT.getCode();
                message = ApiResponseEnum.EXPIRED_PROJECT.getMessage();

            }
            return new ResponseEntity<>(ApiResponse.res(code, message), HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<>(new ResponseDto<>(code, "라벨링 작업요청 성공", labelHIstoryDto), HttpStatus.OK);
    }

    /* 라벨링작업 저장 */
    @PostMapping("saveLabeling")
    @Operation(summary= "라벨링 작업 저장", description = "라벨링 작업 후 임시저장의 개념으로 저장되며, 쿼리에 변화 없음")
    public ResponseEntity<?> getLabelingHistory(@RequestBody UserAnnotatoinReqDto userAnnotatoinReqDto) {

        userJobItemService.save(userAnnotatoinReqDto);

        return new ResponseEntity<>(new ResponseDto<>(200, "라벨링 작업저장 성공", null), HttpStatus.OK);
    }



    /* 라벨링작업 완료 */
    @PostMapping("/completeLabeling")
    @Operation(summary= "라벨링 작업 완료", description = "라벨링 작업 완료되며 상태값이 변경되며 완료된 파일은 다운로드 가능")
    public ResponseEntity<?> completeLabelingHistory(@RequestBody UserAnnotatoinReqDto userAnnotatoinReqDto) {

        userJobItemService.completesave(userAnnotatoinReqDto);

        return new ResponseEntity<>(new ResponseDto<>(200, "라벨링 작업완료 성공", null), HttpStatus.OK);
    }

    /*비식별화된 이미지 + 라벨링 정보 json파일 zip저장*/
    //    ResponseEntity를 사용하면 application/zip 반환에서 오류 발생 -> HttpServletResponse 사용하여 파일 데이터를 스트림으로 변환하여 반환
    @PostMapping("/downloadFiles")
    @Operation(summary = "작업한 파일들 다운로드(zip)", description = "비식별화 진행한 이미지와 라벨링 정보(json)를 zip파일로 다운로드")
    public ResponseEntity<?> downloadFiles(@RequestBody List<Map<String, Long>> fileIds, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8"); // 응답의 문자 인코딩을 UTF-8로 설정
        try {
            // 모든 fileId를 검증
            for (Map<String, Long> fileIdMap : fileIds) {
                Long fileId = fileIdMap.get("fileId");
                if (!userFileService.existsById(fileId)) {
//                    return ResponseEntity.status(HttpStatus.NOT_FOUND)
//                            .contentType(MediaType.APPLICATION_JSON)
//                            .body("존재하지 않는 파일정보 입니다. fileId: " + fileId);
                    return new ResponseEntity<>(new ResponseDto<>(500, "존재하지 않는 정보 입니다.", null), HttpStatus.NOT_FOUND);
                }
            }

            // 모든 fileId가 유효하면 다운로드 진행
            userJobItemService.downloadFiles(fileIds, response);
            return ResponseEntity.ok().build();
//            return new ResponseEntity<>(new ResponseDto<>(200, "zip 다운로드 완료입니다.", null), HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(e.getMessage());
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("파일을 생성하는 동안 오류가 발생했습니다.");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("알 수 없는 오류가 발생했습니다.");
        }
    }
    }