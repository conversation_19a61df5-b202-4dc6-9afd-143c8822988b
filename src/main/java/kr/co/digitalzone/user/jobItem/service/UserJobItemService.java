package kr.co.digitalzone.user.jobItem.service;

import com.fasterxml.jackson.core.JsonEncoding;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import jakarta.persistence.EntityManager;
import jakarta.servlet.http.HttpServletResponse;
import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.user.jobItem.domain.JobLabel;
import kr.co.digitalzone.user.classItem.domain.ClassItem;
import kr.co.digitalzone.user.classItem.repository.UserClassItemRepository;
import kr.co.digitalzone.user.common.request.SearchRequest;
import kr.co.digitalzone.user.file.domain.ProjectFile;
import kr.co.digitalzone.user.file.repository.FileRepository;
import kr.co.digitalzone.user.file.service.UserFileService;
import kr.co.digitalzone.user.file.util.FileUtils;
import kr.co.digitalzone.user.jobItem.repository.*;
import kr.co.digitalzone.user.jobItem.request.UserJobItemReqDto.UserAnnotatoinReqDto;
import kr.co.digitalzone.user.joblabel.repository.UserLabelMngRepository;
import kr.co.digitalzone.user.project.domain.Project;
import kr.co.digitalzone.user.project.repository.UserProjectRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static kr.co.digitalzone.user.jobItem.request.UserJobItemReqDto.UserAnnotationsReqDto;
import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.*;
import static kr.co.digitalzone.user.project.domain.QProject.project;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = false)
public class UserJobItemService {
//    private final UserJobItemRepository userJobItemRepository;
    private final UserBlurJobQueryRepository userBlurJobQueryRepository;
//    private final UserJobListQueryRepository userJobListQueryRepository;
    private final UserJobItemFileRepository userJobItemFileRepository;
    private final UserBlurLablelJobQRepository blurLablelJobQRepository;
    private final UserClassItemRepository classItemRepository;
    private final UserLabelMngRepository labelMngRepository;
    private final UserFileService userfileService;
    private final UserProjectRepository userProjectRepository;
    private final EntityManager em;
    private final FileRepository fileRepository;
    // 파일 다운로드 경로
    @Value("${spring.file.download.path}")
    String downloadPath;

    // 파일 업로드 경로
    @Value("${spring.file.upload.path}")
    String uploadPath;

    @Value("${spring.file.worked.path}")
    String workedPath;

    /* 비식별화 이미지 및 라벨링 json데이터 zip 다운로드  완료*/
    public void downloadFiles(List<Map<String, Long>> fileIds, HttpServletResponse response) throws IOException {
        try {
            // 모든 fileId를 검증
            for (Map<String, Long> fileIdMap : fileIds) {
                Long fileId = fileIdMap.get("fileId");
                if (!fileRepository.existsById(fileId)) {
                    throw new IllegalStateException("존재하지 않는 파일정보 입니다. fileId: " + fileId);
                }
            }

            // 현재시간
            LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Seoul"));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
            String nowTime = now.format(formatter);

            // 압축파일 생성
            String zipFileName = "labeling_data_" + nowTime + ".zip";
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + zipFileName + "\"");

            // 작업 디렉토리 생성
            String workDirPath = downloadPath + "/labeling_data_" + nowTime;
            File workDir = new File(workDirPath);
            if (!workDir.exists()) {
                workDir.mkdirs();
            }

            // ZipOutputStream 생성
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {

                String jsonFilePath = workDir + "/labeling_data_" + nowTime + ".json";
                FileUtils.createFileWithDirectories(jsonFilePath);
                JsonFactory factory = JsonFactory.builder().build();
                JsonGenerator jsonGenerator = factory.createGenerator(new File(jsonFilePath), JsonEncoding.UTF8);
                jsonGenerator.useDefaultPrettyPrinter(); // JSON 파일을 읽기 쉽게 출력

                jsonGenerator.writeStartArray();
                for (Map<String, Long> fileId : fileIds) {
                    ProjectFile projectFile = fileRepository.findById(fileId.get("fileId"))
                            .orElseThrow(() -> new IllegalStateException("존재하지 않는 파일정보 입니다. fileId: " + fileId.get("fileId")));

                    jsonGenerator.writeStartObject();
                    jsonGenerator.writeNumberField("fileId", projectFile.getFileId());
                    jsonGenerator.writeStringField("fileName", projectFile.getFileName());

                    jsonGenerator.writeArrayFieldStart("labelList");
                    List<UserAnnotationsRespDto> annotationsDto = blurLablelJobQRepository.getAnnotatationInfo(fileId.get("fileId"));

                    for (UserAnnotationsRespDto labels : annotationsDto) {
                        jsonGenerator.writeStartObject();
                        jsonGenerator.writeStringField("labelBbox", labels.getLabelBbox());
                        jsonGenerator.writeStringField("labelType", labels.getLabelType());
                        jsonGenerator.writeEndObject();
                    }
                    jsonGenerator.writeEndArray();
                    jsonGenerator.writeEndObject();
                }
                jsonGenerator.writeEndArray();
                jsonGenerator.close();

                // JSON 파일을 작업 디렉토리에 저장
                File jsonFile = new File(jsonFilePath);
                if (!jsonFile.exists()) {
                    throw new IllegalStateException("JSON 파일이 생성되지 않았습니다: " + jsonFilePath);
                }

                // 이미지 파일들을 작업 디렉토리에 복사
                for (Map<String, Long> fileId : fileIds) {
                    Long id = fileId.get("fileId");
                    ProjectFile projectFile = fileRepository.findById(id)
                            .orElseThrow(() -> new IllegalStateException("존재하지 않는 파일정보 입니다. fileId: " + id));
                    //project에서 blurYn이 Y이고 projectFile에서 fileBluring이 Y인 경우에만 파일을 복사

                    if ("Y".equals(projectFile.getFileBluring())&& "Y".equals(projectFile.getProject().getBlurYn())) {
                        String source = projectFile.getFileUrl();
                        String sourceFilePath = workedPath + source;
                        String destinationFilePath = workDir + "/" + new File(sourceFilePath).getName();

                        File sourceFile = new File(sourceFilePath);
                        if (!sourceFile.exists()) {
                            throw new IllegalStateException("소스 파일이 존재하지 않습니다: " + sourceFilePath);
                        }

                        // 파일이 비어 있는지 확인
                        if (Files.size(Paths.get(sourceFilePath)) == 0) {
                            throw new IllegalStateException("소스 파일이 비어 있습니다: " + sourceFilePath);
                        }
                        //
                        Path sourcePath = Paths.get(sourceFilePath);
                        Path destinationPath = Paths.get(destinationFilePath);
                        try {
                            Files.copy(sourcePath, destinationPath, StandardCopyOption.REPLACE_EXISTING);
                        } catch (IOException e) {
                            throw new IllegalStateException("파일 복사 중 오류가 발생했습니다: " + sourceFilePath, e);
                        }
                    } else {
                        // blur_Yn이 N인 경우 처리 로직
                        // 예: 로그 출력
                        log.info("비식별화가 적용되지 않은 파일입니다. fileId: " + id);
                    }
                }
                // 작업 디렉토리 전체를 압축
                File[] files = workDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        try (FileInputStream inputStream = new FileInputStream(file)) {
                            ZipEntry zipEntry = new ZipEntry(file.getName());
                            zipOut.putNextEntry(zipEntry);

                            byte[] buffer = new byte[1024];
                            int bytesRead;
                            while ((bytesRead = inputStream.read(buffer)) != -1) {
                                zipOut.write(buffer, 0, bytesRead);
                            }
                            zipOut.closeEntry();
                        } catch (IOException e) {
                            throw new IllegalStateException("파일 압축 중 오류가 발생했습니다: " + file.getName(), e);
                        }
                    }
                }
                zipOut.finish();
                response.flushBuffer(); // 응답 버퍼 강제 플러시
            }
        } catch (IOException e) {
            log.error("파일을 생성하는 동안 오류가 발생했습니다.", e);

        }
    }

    /* 탭화면 정보조회 */
//    public UserJobItemResponse findJobList(Long projectId) {
//        return userJobListQueryRepository.findJobList(projectId);
//    }

    /* 비식별화 작업목록 조회 */
    public Page<UserTabListResponse> findBlurJob(Long projectId, Pageable pageable, SearchRequest searchRequest) {
        String keyword = searchRequest.getKeyword();
        return userBlurJobQueryRepository.findBlurJob(projectId, pageable, keyword);
    }

    /* 라벨링 작업목록 조회 */
    public Page<UserTabListResponse> findLabelJob(Long projectId, Pageable pageable, SearchRequest searchRequest) {
        String keyword = searchRequest.getKeyword();
        return userBlurJobQueryRepository.findLabelJob(projectId, pageable, keyword);
    }

    /* 완료내역 작업목록 조회 */
    public Page<UserTabListResponse> findCompleteJob(Long projectId, Pageable pageable, SearchRequest searchRequest) {
        String keyword = searchRequest.getKeyword();
        return userBlurJobQueryRepository.findCompleteJob(projectId, pageable, keyword);
    }

    /* 비식별화 초기화 */
    @Value("${spring.file.upload.path}")
    String filePath;

    @Transactional
    public void blurReset(List<Map<String, Long>> blurResetReq) {
        List<Long> fileIdList = blurResetReq
                .stream()
                .map(item -> item.get("fileId")).collect(Collectors.toList());
        ProjectFile projectFile = userJobItemFileRepository.findById(fileIdList.get(0)).orElseThrow(() -> new IllegalStateException("존재하지않는 파일정보 입니다."));
        String delFilePath = filePath + projectFile.getFileUrl();

        labelMngRepository.resetLabel(fileIdList); //anntation정보(jobLabel) 초기화

        if (projectFile.getProject().getBlurYn().equals("Y") && projectFile.getFileBluring().equals("Y")) { //비식별화 사용하고 비식별화 작업진행 한 경우
            File file = new File(delFilePath);
            if (file.exists()) { //비식별화 작업파일이 존재하면  파일삭제 진행
                file.delete();
                userJobItemFileRepository.blurSttusReset(fileIdList); //url정보를 원본파일 경로로 변경, 비식별화 상태변경
            } else {
                System.out.println("파일이없거나 비식별화 미진행으로 비식별화파일 삭제불가.");
            }
        } else {//비식별화 미사용인 경우
            userJobItemFileRepository.labelSttusReset(fileIdList); //라벨링상태(file_labeling)만변경
        }
    }

    /* 비식별화 작업요청 */
    public UserFileInfoByfidRepsDto getBlurHistory(Long fileId)  throws Exception{
        UserFileInfoByfidRepsDto result = blurLablelJobQRepository.getFileInfo(fileId); // 파일정보요청

        if(checkExpired(result.getEndAt())){
            // 만료일이 지났으면 예외처리
            throw new Exception(ApiResponseEnum.EXPIRED_PROJECT.getMessage());
        }
        return result;
    }

    boolean checkExpired(String endAt){
        LocalDateTime now = LocalDateTime.now();    // 현재 시간
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime endAtTime = LocalDate.parse(endAt, formatter).plusDays(1).atStartOfDay();
        return now.isAfter(endAtTime);
    }

    /* 비식별화작업저장 */
    @Transactional
    public void saveBlurJob(Long fileId, MultipartFile file) {
        ProjectFile projectFile = userJobItemFileRepository.findById(fileId).orElseThrow(()->new IllegalStateException("존재하지않는 파일정보 입니다."));

        userfileService.uploadBlurImg(file,projectFile); // 비식별화 파일저장
        String url = projectFile.getFileUrl();
        String real = projectFile.getFileReal();
        String fileExt = projectFile.getFileExt();
        url = url.replace(fileExt, "dat."+fileExt);
        real = real+".dat";

        projectFile.blurSave(real, url);    // file 경로 수정

        // 프로젝트 블러 최종수정일 업데이트
        Project project = userProjectRepository.findById(projectFile.getProject().getProjectId()).get();
        project.setBlurUpdateAt(LocalDateTime.now());
    }

    /* 비식별화작업 완료 */
    @Transactional
    public void completeBlurJob(Long fileId, MultipartFile file) {
        ProjectFile projectFile = userJobItemFileRepository.findById(fileId).orElseThrow(()->new IllegalStateException("존재하지않는 파일정보 입니다."));

        userfileService.uploadBlurImg(file,projectFile); // 비식별화 파일저장
        if(projectFile.getFileBluring().equals("N")){ // 비식별화작업 완료인 경우만 DB정보변경
            String url = projectFile.getFileUrl();
            String real = projectFile.getFileReal();
            String fileExt = projectFile.getFileExt();
            url = url.replace(fileExt, "dat."+fileExt);
            real = real+".dat";

            projectFile.blurComp(real, url,"Y");

            // 프로젝트 블러 최종수정일 업데이트
            Project project = userProjectRepository.findById(projectFile.getProject().getProjectId()).get();
            project.setBlurUpdateAt(LocalDateTime.now());
//            userJobItemFileRepository.saveBlurJob(fileId);
        }

        if(projectFile.getFileLabeling().equals("Y")){ //해당파일이 라벨링 작업을 하였다면 파일상태(file_status)를 검수중(E)로 변경
            userJobItemFileRepository.updateFileSttus(fileId);
        }
    }

    /* 라벨링작업요청 */
    public UserLabelHIstoryRespDto loadLabelingJob(Long projectId, Long fileId) throws Exception{

        List<UserClassInfoByPjcRespDto> clsssinfoClassInfoByPjcDto = blurLablelJobQRepository.getClassInfo(projectId); // 클래스목록조회
        UserFileInfoByfidRepsDto fileInfoByfidDto = blurLablelJobQRepository.getFileInfo(fileId); // 파일정보조회
        List<UserAnnotationsRespDto> annotationsDto = blurLablelJobQRepository.getAnnotatationInfo(fileId); // 라벨링정보조회

        if(checkExpired(fileInfoByfidDto.getEndAt())){
            // 만료일이 지났으면 예외처리
            throw new Exception(ApiResponseEnum.EXPIRED_PROJECT.getMessage());
        }
        UserLabelHIstoryRespDto result = new UserLabelHIstoryRespDto(clsssinfoClassInfoByPjcDto, fileInfoByfidDto, annotationsDto); //라벨링작업시 모든정본

        return result;
    }

    /* 라벨링작업 완료 */
    @Transactional
    public void save(UserAnnotatoinReqDto userAnnotatoinReqDto) {
        List<UserAnnotationsReqDto> annotationinfo = userAnnotatoinReqDto.getAnnotations();

        // 이전 라벨링 데이터 삭제
        labelMngRepository.resetLabelInfo(annotationinfo.get(0).getFileId());

        for (int i = 0; i < annotationinfo.size(); i++) { // 라벨링aanotiaon값저장
            ClassItem classItem = classItemRepository.findById(annotationinfo.get(i).getClassId()).orElseThrow(() -> new IllegalStateException("존재하지않는 클래스정보 입니다."));
            ProjectFile projectFile = userJobItemFileRepository.findById(annotationinfo.get(i).getFileId()).orElseThrow(() -> new IllegalStateException("존재하지않는 파일정보 입니다."));
            Project project = userProjectRepository.findById(projectFile.getProject().getProjectId()).orElseThrow(() -> new IllegalStateException("존재하지않는 프로젝트정보 입니다."));

            JobLabel jobLabel = JobLabel.builder()
//                    .ccode(ccode)
//                    .labelArea(annotationinfo.get(i).getLabelArea())
                    .labelType(annotationinfo.get(i).getLabelType())
                    .labelBbox(annotationinfo.get(i).getLabelBbox())
                    .classItem(classItem)
                    .projectFile(projectFile)
                    .delYn("N")
                    .build();
            labelMngRepository.save(jobLabel);
            projectFile.setUpdateAt(LocalDateTime.now());
            project.setLabelUpdateAt(LocalDateTime.now()); // 라벨링 최종 수정일 업데이트
            em.flush();
        }
    }


    /* 라벨링작업 완료 */
    @Transactional
    public void completesave(UserAnnotatoinReqDto userAnnotatoinReqDto) {
        List<UserAnnotationsReqDto> annotationinfo = userAnnotatoinReqDto.getAnnotations();

        // 이전 라벨링 데이터 삭제
        labelMngRepository.resetLabelInfo(annotationinfo.get(0).getFileId());

        for (int i = 0; i < annotationinfo.size(); i++) { // 라벨링aanotiaon값저장
            ClassItem classItem = classItemRepository.findById(annotationinfo.get(i).getClassId()).orElseThrow(()->new IllegalStateException("존재하지않는 클래스정보 입니다."));
            ProjectFile projectFile = userJobItemFileRepository.findById(annotationinfo.get(i).getFileId()).orElseThrow(()->new IllegalStateException("존재하지않는 파일정보 입니다."));
            Project project = userProjectRepository.findById(projectFile.getProject().getProjectId()).orElseThrow(()->new IllegalStateException("존재하지않는 프로젝트정보 입니다."));

            JobLabel jobLabel = JobLabel.builder()
//                    .ccode(ccode)
//                    .labelArea(annotationinfo.get(i).getLabelArea())
                    .labelType(annotationinfo.get(i).getLabelType())
                    .labelBbox(annotationinfo.get(i).getLabelBbox())
                    .classItem(classItem)
                    .projectFile(projectFile)
                    .delYn("N")
                    .build();
            labelMngRepository.save(jobLabel);

            em.flush();

            if (annotationinfo.size() == i + 1) {  // 라벨링작업상태변경
                projectFile.labelSave("Y");

                project.setLabelUpdateAt(LocalDateTime.now()); // 라벨링 최종 수정일 업데이트
                project.setCompUpdateAt(LocalDateTime.now()); // 완료 최종 수정일 업데이트

//                userJobItemFileRepository.saveLabelJob(projectFile.getFileId()); //비식별화상태(file_labeling)을 변경
                if("Y".equals(project.getBlurYn()) && "Y".equals(projectFile.getFileBluring())){  //비식별화를 사용하고 비식별화 작업이 완료된 경우에만, 파일상태를 검수중(E)로 변경
                    userJobItemFileRepository.updateFileSttus(projectFile.getFileId());
                } else if("N".equals(project.getBlurYn())){ //비식별화를 사용하지 않는다면 라벨링 작업을 저장한경우 파일상태를 검수중으로 변경
                    userJobItemFileRepository.updateFileSttus(projectFile.getFileId());
                }
            }
          }
        }
    }
