package kr.co.digitalzone.user.jobItem.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import kr.co.digitalzone.user.common.domain.CommonEntity;
import kr.co.digitalzone.user.file.domain.ProjectFile;
import kr.co.digitalzone.user.classItem.domain.ClassItem;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@SequenceGenerator(
        name = "LABEL_ID",
        sequenceName = "seq_label_id",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_JOB_LABEL")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class JobLabel extends CommonEntity {
    /* 작업파일키 PK */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE, // 사용할 전략을 시퀀스로 선택 SEQUENCE
            generator="LABEL_ID"              // 식별자 생성기를 설정해놓은 OPERT_NO 설정
    )
    @Column(name = "LABEL_ID")
    private Long labelId;

//    /* 회사코드 */
//    @NotNull
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 파일아이디 FK */
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "FILE_ID")
    private ProjectFile projectFile;

    /* 클래스아이디 FK */
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLASS_ID")
    private ClassItem classItem;

    /* 라벨타입 */
    @NotNull
    @Column(name = "LABEL_TYPE", length = 30)
    private String labelType;

//    /* 라벨넓이 */
//    @Column(name = "LABEL_AREA", length = 10)
//    private String labelArea;

    /* 라벨박스 */
    @Column(name = "LABEL_BBOX", length = 50000)
    private String labelBbox;

    /* 삭제여부 */
    @NotNull
    @Column(name = "DEL_YN", length = 10)
    private String delYn;

    @Builder
    public JobLabel(Long labelId,ProjectFile projectFile, ClassItem classItem, String labelType, String labelBbox, String delYn) {
        this.labelId = labelId;
//        this.ccode = ccode;
        this.projectFile = projectFile;
        this.classItem = classItem;
        this.labelType = labelType;
//        this.labelArea = labelArea;
        this.labelBbox = labelBbox;
        this.delYn = delYn;
    }
}
