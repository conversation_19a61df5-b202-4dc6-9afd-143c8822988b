//package kr.co.digitalzone.user.jobItem.repository;
//
//import com.querydsl.core.types.dsl.CaseBuilder;
//import com.querydsl.core.types.dsl.Expressions;
//import com.querydsl.jpa.impl.JPAQueryFactory;
//import jakarta.persistence.EntityManager;
//import kr.co.digitalzone.user.jobItem.response.QUserJobItemRespDto_UserJobItemResponse;
//import lombok.RequiredArgsConstructor;
//import org.springframework.stereotype.Repository;
//
//import static kr.co.digitalzone.entity.Qcomm_users.comm_users;
//import static kr.co.digitalzone.user.jobItem.response.UserJobItemRespDto.UserJobItemResponse;
//import static kr.co.digitalzone.user.project.domain.QProject.project;
//
//@Repository
//@RequiredArgsConstructor
//public class UserJobListQueryRepository {
//    private final EntityManager em;
//
////    /* 탭화면 정보조회 */
////    public UserJobItemResponse findJobList(Long projectId) {
////                JPAQueryFactory query = new JPAQueryFactory(em);
////        return query.select(
////                new QUserJobItemRespDto_UserJobItemResponse(
////                          project.projectId
////                        , project.projectName
////                        , project.registeredAt
////                        , project.endAt
////                        , project.projectCmt
////                        , new CaseBuilder()
////                                .when(project.blurYn.eq("Y")).then("사용함")
////                                .otherwise("미사용")
////                        , Expressions.stringTemplate("STRING_AGG({0},',')", comm_users.name).as("name")
//////                        , new CaseBuilder()
//////                                .when(project.autoLabelYn.eq("Y")).then("사용함")
//////                                .otherwise("미사용")
////                        )
////                )
////                .from(project)
////                .innerJoin(comm_users)
////                .on(project.userEmail.eq(comm_users.email))
////                .where(
////                        project.projectId.eq(projectId))
////                .groupBy(project.projectId)
////                .fetchOne();
////    }
////
////
////}