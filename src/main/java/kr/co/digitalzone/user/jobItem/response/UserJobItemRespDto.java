package kr.co.digitalzone.user.jobItem.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.querydsl.core.annotations.QueryProjection;
import kr.co.digitalzone.user.common.dto.ResponsePageDto;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class UserJobItemRespDto {

    // 탭화면 정보조회
    @Getter @Setter
    public static class UserJobItemResponse {
        private Long projectId;
        private String projectName;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime registeredAt;
        private String endAt;
        private String projectCmt;
        private String blurYn;
        private String name;
//        Page<?> fileList;
//        ResponsePageDto<?> fileList;

        @QueryProjection
        public UserJobItemResponse(Long projectId, String projectName, LocalDateTime registeredAt, String endAt, String projectCmt, String blurYn, String name) {
            this.projectId = projectId;
            this.projectName = projectName;
            this.registeredAt = registeredAt;
            this.endAt = endAt;
            this.projectCmt = projectCmt;
            this.blurYn = blurYn;
            this.name = name;
        }
    }

    // 작업자 조회
    @Getter @Setter
    public static class UserNameResponse {
        private String name;

        @QueryProjection
        public UserNameResponse(String name) {
            this.name = name;
        }
    }

    // 탭화면 작업목록 조회
    @Getter @Setter
    public static class UserTabListResponse {
        private Long fileId;
        private String fileName;
        private String fileType;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateAt;
        private String updatedBy;
//        private String fileStatus;
        private String fileUrl;
        @QueryProjection
        public UserTabListResponse(Long fileId, String fileName, String fileType, LocalDateTime updateAt, String updatedBy, String fileUrl) {
            this.fileId = fileId;
            this.fileName = fileName;
            this.fileType = fileType;
            this.updateAt = updateAt;
            this.updatedBy = updatedBy;
//            this.fileStatus = fileStatus;
            if (fileUrl.contains(".dat")) {
                this.fileUrl = "/thumbnail" + fileUrl.replaceAll(".dat", "");
            } else {
                this.fileUrl = "/thumbnail" + fileUrl;
            }

        }
    }

    // 검수완료 작업목록 조회
    @Getter @Setter
    public static class UserApproveResponse {
        private Long fileId;
        private String fileName;
        private String fileType;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateAt;
        private String updatedBy;
        private String fileStatus;
        private String fileUrl;
        @QueryProjection
        public UserApproveResponse(Long fileId, String fileName, String fileType, LocalDateTime updateAt, String updatedBy, String fileStatus, String fileUrl) {
            this.fileId = fileId;
            this.fileName = fileName;
            this.fileType = fileType;
            this.updateAt = updateAt;
            this.updatedBy = updatedBy;
            this.fileStatus = fileStatus;
            if (fileUrl.contains(".dat")) {
                this.fileUrl = "/thumbnail" + fileUrl.replaceAll(".dat", "");
            } else {
                this.fileUrl = "/thumbnail" + fileUrl;
            }
        }
    }
    
    // 파일번호별 라벨링조회
    @Getter @Setter
    public static class UserAnnotationsRespDto {
        private Long labelId;
        private Long fileId;
        private Long classId;
        private String labelType;
        private String classColor;
//        private String labelArea;
        private String labelBbox;

        @QueryProjection
        public UserAnnotationsRespDto(Long labelId, Long fileId, Long classId, String labelType, String labelBbox,String classColor) {
            this.labelId = labelId;
            this.fileId = fileId;
            this.classId = classId;
            this.labelType = labelType;
//            this.labelArea = labelArea;
            this.classColor = classColor;
            this.labelBbox = labelBbox;
        }
    }

    // 프로젝트 번호별 클래스 정보조회
    @Getter @Setter
    public static class UserClassInfoByPjcRespDto {

        private Long classId;
        private String className;
        private String classColor;
        private String classType;
        @QueryProjection
        public UserClassInfoByPjcRespDto(Long classId, String className, String classColor, String classType) {
            this.classId = classId;
            this.className = className;
            this.classColor = classColor;
            this.classType = classType;
        }
    }

    // 라벨링 작업 데이터 조회
    @Getter @Setter
    public static class UserLabelHIstoryRespDto {
        private List<UserClassInfoByPjcRespDto> classInfo;
        private UserFileInfoByfidRepsDto imageInfo;
        private List<UserAnnotationsRespDto> annotations;

        @QueryProjection
        public UserLabelHIstoryRespDto(List<UserClassInfoByPjcRespDto> classInfoByPjcDtoList, UserFileInfoByfidRepsDto fileInfoByfidDto, List<UserAnnotationsRespDto> annotationsDto) {
            this.imageInfo = fileInfoByfidDto;
            this.classInfo = classInfoByPjcDtoList;
            this.annotations = annotationsDto;


        }
    }
    
    // 라벨링,비식별화 파일정보 조회
    @Getter @Setter
    public static class UserFileInfoByfidRepsDto {
        private Long fileId;
        private String fileUrl;
        private String projectName;
        private String fileName;
        private String endAt;

        @QueryProjection
        public UserFileInfoByfidRepsDto(Long fileId, String fileUrl, String projectName, String fileName, String endAt){
            this.fileId = fileId;
            if (fileUrl.contains(".dat")) {
                this.fileUrl = "/worked" + fileUrl;
            } else {
                this.fileUrl = "/upload" + fileUrl;
            }
            this.fileName = fileName;
            this.projectName = projectName;
            this.endAt = endAt;
//            this.jobName =jobName;
        }
    }

    // 프로젝트/작업목록 내역조회
    @Getter @Setter
    public static class UserJobListResponse {
        private Long projectId;
        private String projectName;
//        private Long jobId;
//        private String jobName;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime registeredAt;
        private String endAt;
        private Long fileCount;
        private Long blurCount;
        private Long labelCount;
        private String progress;
//        private String jobStatus;

        @QueryProjection
        public UserJobListResponse(Long projectId, String projectName, LocalDateTime registeredAt, String endAt, Long fileCount) {
            this.projectId = projectId;
            this.projectName = projectName;
//            this.jobId = jobId;
//            this.jobName = jobName;
            this.registeredAt = registeredAt;
            this.endAt = endAt;
            this.fileCount = fileCount;
//            this.jobStatus = jobStatus;
        }
    }

    // 작업목록 비식별화, 라벨 수
    @Getter @Setter
    public static class UserJobBlurResponse {
        private Integer blurCount;
        private Integer labelCount;
        @QueryProjection
        public UserJobBlurResponse(Integer blurCount, Integer labelCount) {
            this.blurCount = blurCount;
            this.labelCount = labelCount;
        }
    }

    // 작업목록 진행률
    @Getter @Setter
    public static class UserJobProgressResponse {
        private Long fileTotalCount;
        private Long fileCount;
        @QueryProjection
        public UserJobProgressResponse(Long fileTotalCount, Long fileCount) {
            this.fileTotalCount = fileTotalCount;
            this.fileCount = fileCount;
        }
    }


}
