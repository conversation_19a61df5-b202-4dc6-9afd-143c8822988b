package kr.co.digitalzone.user.classItem.controller;

import jakarta.validation.Valid;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.user.project.domain.Project;
import kr.co.digitalzone.user.classItem.service.ProjectClassService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class ProjectClassController {
    private final ProjectClassService projectClassService;

    /* 클래스링크 저장 */
    @PostMapping
    public ResponseEntity<?> save(@RequestPart @Valid List<Map<String, Long>> classReq, Project project) {
        projectClassService.save(classReq, project);

        return new ResponseEntity<>(new ResponseDto<>(200, "클래스정보링크 저장 성공", null), HttpStatus.OK);
    }
}
