package kr.co.digitalzone.user.classItem.repository;

import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.classItem.response.QClassItemRespDto_ClassItemListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.querydsl.jpa.JPAExpressions.select;
import static kr.co.digitalzone.entity.Qcomm_users.comm_users;
import static kr.co.digitalzone.user.classItem.domain.QClassItem.classItem;
import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.ClassItemListResponse;

@Repository
@RequiredArgsConstructor
public class ClassItemListQueryRepository {
    private final EntityManager em;
    public Page<ClassItemListResponse> findAll(String email, Pageable pageable, String keyword) {

        JPAQueryFactory query = new JPAQueryFactory(em);

        List<ClassItemListResponse> content = query.select(
                        new QClassItemRespDto_ClassItemListResponse(
                                classItem.classId
                                , classItem.email
                                , classItem.className
                                , classItem.classColor
                                , classItem.classType
//                                , new CaseBuilder()
//                                .when(classItem.classType.eq("B")).then("Bounding Box")
//                                .when(classItem.classType.eq("P")).then("Polygon")
//                                .otherwise("Key Point")
                                , classItem.registeredAt
                                )
                )
                .from(classItem)
                .where(
                          classItem.delYn.eq("N")
//                        , classItem.ccode.eq(ccode)
                        , classItem.className.contains(keyword)
                        ,classItem.email.eq(email)
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(classItem.classId.desc())
                .fetch();

        JPAQuery<Long> countQuery = query
                .select(classItem.count())
                .from(classItem)
                .where(
                          classItem.delYn.eq("N")
//                        , classItem.ccode.eq(ccode)
                        , classItem.className.contains(keyword)
                        ,classItem.email.eq(email)
                );

        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
    }
}