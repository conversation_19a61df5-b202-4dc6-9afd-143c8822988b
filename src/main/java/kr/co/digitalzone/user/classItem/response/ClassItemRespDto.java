package kr.co.digitalzone.user.classItem.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.querydsl.core.annotations.QueryProjection;
import kr.co.digitalzone.user.classItem.domain.ClassItem;
import lombok.*;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClassItemRespDto {
    // 클래스 생성
    @Getter @Setter
    public static class ClassItemSaveResponse{
        private Long classId;
//        private String ccode;
        private String className;
        private String classColor;
        private String classType;
        public ClassItemSaveResponse(ClassItem classItem) {
            this.classId = classItem.getClassId();
            this.classColor = classItem.getClassColor();
            this.className = classItem.getClassName();
            this.classType = classItem.getClassType();
        }
    }

    // 클래스 조회
    @Getter @Setter
    public static class ClassItemListResponse{
        private Long classId;
        private String email;
        private String className;
        private String classColor;
        private String classType;
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Seoul")
        private LocalDateTime registeredAt;

        @QueryProjection
        public ClassItemListResponse(Long classId, String email, String className, String classColor, String classType, LocalDateTime registeredAt) {
            this.classId = classId;
            this.email = email;
            this.className = className;
            this.classColor = classColor;
            this.classType = classType;
            this.registeredAt = registeredAt;
        }
    }

    // 클래스 수정
    @Getter @Setter
    public static class ClassItemUpdateResponse{
        private Long classId;
        private String email;
        private String className;
        private String classColor;
        private String classType;
        @QueryProjection
        public ClassItemUpdateResponse(ClassItem classItem) {
            this.classId = classItem.getClassId();
            this.classColor = classItem.getClassColor();
            this.className = classItem.getClassName();
            this.classType = classItem.getClassType();
        }
    }

    // json파일
    @Getter @Setter
    public static class JsonFileListResponse{
        private Long classId;
        private String className;
        private String classColor;
        private String classType;

        @QueryProjection
        public JsonFileListResponse(Long classId, String className, String classColor, String classType) {
            this.classId = classId;
            this.className = className;
            this.classColor = classColor;
            this.classType = classType;
        }
    }
}
