package kr.co.digitalzone.user.classItem.service;

import kr.co.digitalzone.user.project.domain.Project;
import kr.co.digitalzone.user.classItem.domain.ClassItem;
import kr.co.digitalzone.user.classItem.domain.ProjectClass;
import kr.co.digitalzone.user.classItem.repository.ClassItemRepository;
import kr.co.digitalzone.user.classItem.repository.ProjectClassRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = false)
public class ProjectClassService {
    private final ProjectClassRepository projectClassRepository;
    private final ClassItemRepository classItemRepository;

    /* 클래스링크 등록 */
    @Transactional
    public void save(List<Map<String, Long>> classReq, Project project) {

        classReq.forEach(item -> {
            Long id = item.get("classId");

            ClassItem classItem = classItemRepository.findById(id)
                    .orElseThrow(() -> new IllegalStateException("클래스가 존재하지 않습니다."));

            ProjectClass projectClass = ProjectClass
                    .builder()
//                    .ccode(project.getCcode())
                    .classItem(classItem)
                    .project(project)
                    .delYn("N")
                    .build();
            projectClassRepository.save(projectClass);
        });
    }

    /* 클래스링크 수정 */
    @Transactional
    public void updateProjectClass(List<Map<String, Long>> projectClassRequset, Project project) {

        List<Long> projectClassList = projectClassRequset
                .stream()
                .map(item -> item.get("linkId")).collect(Collectors.toList());
        projectClassRepository.projectClassDelete(projectClassList);
    }
}