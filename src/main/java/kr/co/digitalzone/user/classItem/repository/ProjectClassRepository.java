package kr.co.digitalzone.user.classItem.repository;

import kr.co.digitalzone.user.classItem.domain.ProjectClass;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectClassRepository extends JpaRepository<ProjectClass, Long> {
    /* 클래스링크 삭제 */
    @Modifying(clearAutomatically = true)
    @Query("update AIL_PROJECT_CLASS p set p.delYn = 'Y', p.updateAt = local_datetime where p.linkId in (:projectClassList)")
    void projectClassDelete(@Param("projectClassList") List<Long> projectClassList);

    /* 클래스링크 수정 */
    @Modifying
    @Query("update AIL_PROJECT_CLASS p set p.delYn = 'Y', p.updateAt = local_datetime where p.project.projectId = :projectId")
    void deleteByProjectClass(@Param("projectId") Long projectId);
}