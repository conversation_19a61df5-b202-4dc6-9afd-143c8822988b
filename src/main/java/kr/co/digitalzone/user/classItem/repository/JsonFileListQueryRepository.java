package kr.co.digitalzone.user.classItem.repository;

import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.classItem.response.QClassItemRespDto_JsonFileListResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

import static kr.co.digitalzone.user.classItem.domain.QClassItem.classItem;
import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.JsonFileListResponse;

@Repository
@RequiredArgsConstructor
public class JsonFileListQueryRepository {
    private final EntityManager em;

    public List<JsonFileListResponse> findClassAll(@Param("classIdList") List<Long> classIdList) {
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(
                new QClassItemRespDto_JsonFileListResponse(
                          classItem.classId
                        , classItem.className
                        , classItem.classColor
                        , classItem.classType
                        )
                )
                .from(classItem)
                .where(classItem.classId.in(classIdList))
                .fetch();
    }
}