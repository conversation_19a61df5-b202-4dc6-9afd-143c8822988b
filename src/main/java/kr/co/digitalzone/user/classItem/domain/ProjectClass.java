package kr.co.digitalzone.user.classItem.domain;

import jakarta.persistence.*;
import kr.co.digitalzone.user.common.domain.CommonEntity;
import kr.co.digitalzone.user.project.domain.Project;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@SequenceGenerator(
        name = "LINK_ID",
        sequenceName = "seq_link_id",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_PROJECT_CLASS")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ProjectClass extends CommonEntity {
    /* 클래스링크아이디 */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE,
            generator="LINK_ID"
    )
    @Column(name = "LINK_ID")
    private Long linkId;

    /* 회사코드 */
//    @NotNull
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 프로젝트아이디 FK */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROJECT_ID")
    private Project project;

    /* 클래스아이디 FK */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLASS_ID")
    private ClassItem classItem;

    /* 삭제여부 */
    @Column(name = "DEL_YN", length = 10)
    private String delYn;

    @Builder
    public ProjectClass(Long linkId, Project project, ClassItem classItem, String delYn) {
        this.linkId = linkId;
//        this.ccode = ccode;
        this.project = project;
        this.classItem = classItem;
        this.delYn = delYn;
    }

    public void setChangeProjectClass(String delYn){
        this.delYn = delYn;
    }
}
