package kr.co.digitalzone.user.classItem.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
//import kr.co.digitalzone.svAdmin.classGroup.domain.ClassChild;
import kr.co.digitalzone.user.common.domain.CommonEntity;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@SequenceGenerator(
        name = "CLASS_ID",
        sequenceName = "seq_class_id",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_CLASS_ITEM")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClassItem extends CommonEntity {
    /* 클래스아이디 */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE,
            generator="CLASS_ID"
    )
    @Column(name = "CLASS_ID")
    private Long classId;

//    /* 회사코드 */
//    @NotNull
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 이메일 */
    @NotNull
    @Column(name = "EMAIL", length = 100)
    private String email;

    /* 클래스명 */
    @NotNull
    @Column(name = "CLASS_NAME", length = 100)
    private String className;

    /* 클래스색상 */
    @NotNull
    @Column(name = "CLASS_COLOR", length = 10)
    private String classColor;

    /* 클래스타입 */
    @NotNull
    @Column(name = "CLASS_TYPE", length = 100)
    private String classType;

    /* 삭제여부 */
    @NotNull
    @Column(name = "DEL_YN", length = 10)
    private String delYn;

    /* 테이블 JOIN */
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "classItem", cascade = CascadeType.ALL)
    private List<ProjectClass> projectClass = new ArrayList<>();

//    @OneToMany(fetch = FetchType.LAZY, mappedBy = "classItem", cascade = CascadeType.ALL)
//    private List<ClassChild> classChild = new ArrayList<>();

//    @OneToMany(fetch = FetchType.LAZY, mappedBy = "classItem", cascade = CascadeType.ALL)
//    private List<JobLabel> jobLabel = new ArrayList<>();

    @Builder
    public ClassItem(Long classId, String email,String className, String classColor, String classType, String delYn, List<ProjectClass> projectClass) {
        this.classId = classId;
//        this.ccode = ccode;
        this.email = email;
        this.className = className;
        this.classColor = classColor;
        this.classType = classType;
        this.delYn = delYn;
        this.projectClass = projectClass;
//        this.classChild = classChild;
//        this.jobLabel = jobLabel;
    }

    public void setChangeProjectClass(String email,String className, String classColor, String classType){
//        this.ccode = ccode;
        this.email = email;
        this.className = className;
        this.classColor = classColor;
        this.classType = classType;
    }

    public void setChangeTest(String className, String classColor, String classType, String delYn){
        this.className = className;
        this.classColor = classColor;
        this.classType = classType;
        this.delYn = delYn;
    }
}
