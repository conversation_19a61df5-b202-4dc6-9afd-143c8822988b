package kr.co.digitalzone.user.classItem.request;

import lombok.*;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ClassItemReqDto {
    // 클래스 생성
    @Getter @Setter
    public static class ClassItemSaveRequest {
        private String email;
        private String className;
        private String classColor;
        private String classType;
    }

    // 클래스 수정
    @Getter @Setter
    public static class ClassItemUpdateRequest {
        private Long classId;
        private String email;
        private String className;
        private String classColor;
        private String classType;
    }

    // 클래스 삭제
    @Getter @Setter
    public static class ClassItemDeleteRequest {
        private Long classId;
    }
}