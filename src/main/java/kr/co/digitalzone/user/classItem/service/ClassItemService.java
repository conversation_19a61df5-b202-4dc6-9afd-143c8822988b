package kr.co.digitalzone.user.classItem.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.common.request.SearchRequest;
import kr.co.digitalzone.user.classItem.domain.ClassItem;
import kr.co.digitalzone.user.classItem.repository.ClassItemListQueryRepository;
import kr.co.digitalzone.user.classItem.repository.ClassItemRepository;
import kr.co.digitalzone.user.classItem.repository.JsonFileListQueryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.FileWriter;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static kr.co.digitalzone.user.classItem.request.ClassItemReqDto.ClassItemSaveRequest;
import static kr.co.digitalzone.user.classItem.request.ClassItemReqDto.ClassItemUpdateRequest;
import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.*;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = false)
public class ClassItemService {
    private final ClassItemRepository classItemRepository;
    private final JsonFileListQueryRepository jsonFileListQueryRepository;
    private final ClassItemListQueryRepository classItemListQueryRepository;
    private final EntityManager em;

    // 동일한 email과 className을 가진 클래스가 존재하는지 확인하는 메서드 (del_yn이 'N'인 경우만)
    public boolean existsByEmailAndClassNameAndDelYn(String email, String className, String delYn) {
        return classItemRepository.existsByEmailAndClassNameAndDelYn(email, className, delYn);
    }
    /* 클래스 생성 */
    @Transactional
    public ClassItemSaveResponse save(ClassItemSaveRequest classSaveReq) {
        ClassItem classItem = ClassItem
                .builder()
                .email(classSaveReq.getEmail())
                .className(classSaveReq.getClassName())
                .classColor(classSaveReq.getClassColor())
                .classType(classSaveReq.getClassType())
                .delYn("N")
                .build();
        classItemRepository.save(classItem);
        em.flush();

        ClassItem classItems = classItemRepository.findAllById(classItem.getClassId());
        return new ClassItemSaveResponse(classItems);
    }

    /* 클래스 불러오기 */
    public Page<ClassItemListResponse> findAllClass(String email,Pageable pageable, SearchRequest searchRequest) {
        String keyword = searchRequest.getKeyword();
        return classItemListQueryRepository.findAll(email, pageable, keyword);
    }

    /* 클래스 수정 */
    @Transactional
    public ClassItemUpdateResponse updateProjectClass(ClassItemUpdateRequest classUpdateReq) {
        //클래스 PK 조회
        ClassItem classItem = classItemRepository.findById(classUpdateReq.getClassId())
                .orElseThrow(() -> new IllegalStateException("생성된 클래스가 존재하지 않습니다."));

        classItem.setChangeProjectClass(
//                 classUpdateReq.getCcode()
                classUpdateReq.getEmail()
                ,classUpdateReq.getClassName()
                ,classUpdateReq.getClassColor()
                ,classUpdateReq.getClassType()
        );
        classItemRepository.save(classItem);
//        em.flush();

        ClassItem classItems = classItemRepository.findAllById(classUpdateReq.getClassId());
        return new ClassItemUpdateResponse(classItems);
    }

    /* 클래스 삭제 */
    @Transactional
    public void deleteProjectClass(List<Map<String, Long>> classDeleteReq) {
        List<Long> classIdList = classDeleteReq
                .stream()
                .map(item -> item.get("classId")).collect(Collectors.toList());
        classItemRepository.deleteClass(classIdList);
    }

//    /* Json파일 추출 */
//    @Transactional
//    public void createFile(List<Map<String, Long>> param) throws Exception {
//        List<Long> classIdList = param
//                .stream()
//                .map(item -> item.get("classId")).collect(Collectors.toList());
//
//        List<JsonFileListResponse> result = jsonFileListQueryRepository.findClassAll(classIdList);
//
//        ObjectMapper mapper = new ObjectMapper();
//        String fileContent = mapper.writeValueAsString(result);
//
//        // 파일 경로
//        String fileLocation = "C:/Users/<USER>/Downloads/";
//        // 확장자 설정
//        String extsn = ".json";
//        // 파일명 설정
//        String jsonName = result.stream().map(o -> o.getClassName()).collect(Collectors.toList()).toString();
//        int idx = jsonName.indexOf(",");
//        String jsonReal = jsonName.substring(0, idx).replaceAll("\\[", "").replaceAll("\\]","") + " 묶음";
//
//        try {
//            // 파일 객체 생성
//            FileWriter file = new FileWriter(fileLocation + jsonReal + extsn);
//
//            file.write(fileContent);
//            file.flush();
//            file.close();
//        } catch (IOException e) {
//            throw new IllegalStateException(e);
//        }
//    }
}