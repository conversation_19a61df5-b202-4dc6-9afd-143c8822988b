package kr.co.digitalzone.user.classItem.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.user.classItem.response.ClassItemRespDto;
import kr.co.digitalzone.user.classItem.service.ClassItemService;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.user.common.dto.ResponsePageDto;
import kr.co.digitalzone.user.common.request.SearchRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static kr.co.digitalzone.user.classItem.request.ClassItemReqDto.ClassItemSaveRequest;
import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.ClassItemListResponse;
import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.ClassItemSaveResponse;
@Slf4j
@Validated
@Tag( name= "클래스 관련 API", description = "생성,삭제,조회 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class ClassItemController {
    private final ClassItemService classItemService;

    int code = ApiResponseEnum.OK.getCode();
    String message = ApiResponseEnum.OK.getMessage();

    /* 클래스 생성 */
    @Operation(summary= "클래스 생성", description = "새 클래스 작성 후 OK 버튼 클릭 시")
    @PostMapping("/saveClassItem")
    public ResponseEntity<?> save(@RequestBody @Valid ClassItemSaveRequest classSaveReq) {

        // Token 안에 있는 email 가져오기
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        classSaveReq.setEmail(email);

        ClassItemSaveResponse result = null; // 클래스 생성 결과
        boolean exists = classItemService.existsByEmailAndClassNameAndDelYn(email, classSaveReq.getClassName(), "N");
        try {

            if (! exists) { // 동일한 email과 className을 가진 클래스가 존재하지 않는 경우
                result = classItemService.save(classSaveReq);
                code = ApiResponseEnum.OK.getCode();
                message = "클래스 생성 성공";
            } else {
            throw new IllegalStateException(ApiResponseEnum.DUPLICATE_CLASSNAME.getMessage());
        }
        } catch (Exception e) {
            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_CLASSNAME.getMessage())) { // 중복된 클래스명인 경우
                code = ApiResponseEnum.DUPLICATE_CLASSNAME.getCode(); // 중복된 클래스명 코드
                message = ApiResponseEnum.DUPLICATE_CLASSNAME.getMessage(); // 중복된 클래스명 메시지
            }
            e.getStackTrace(); // 에러 메시지 출력
        }

        return new ResponseEntity<>(new ResponseDto<>(code, message, result), HttpStatus.OK);
    }

    /* 클래스 찾기/불러오기 */
    @GetMapping("/findClassItem")
    @Operation(summary= "클래스 목록 불러오기", description = "클래스 탭 접속하면 클래스 목록 불러오기 (백엔드API 활용하고 있음)")
    public ResponseEntity<?> findAllClass(@RequestHeader("email") String email,
                                          @PageableDefault(page = 0, size = 10, sort = "id", direction = Sort.Direction.DESC) Pageable pageable,
                                          SearchRequest searchRequest) {
        Page<ClassItemListResponse> result = classItemService.findAllClass(email,pageable, searchRequest);
        log.info("잘못된 요청", result.toString());
        return new ResponseEntity<>(new ResponsePageDto<>(200, "클래스 불러오기 성공", result), HttpStatus.OK);
    }

//    /* 클래스 수정 */
//    @PostMapping("/updateClassItem")
//    public ResponseEntity<?> updateProjectClass(@RequestBody @Valid ClassItemUpdateRequest classUpdateReq){
//        ClassItemUpdateResponse result = classItemService.updateProjectClass(classUpdateReq);
//        return new ResponseEntity<>(new ResponseDto<>(200, "클래스 수정 성공", result), HttpStatus.OK);
//    }

    /* 클래스 삭제 */
    @Operation(summary= "클래스 삭제", description = "클래스 삭제 버튼 누르고 확인 버튼 누르면 삭제")
    @PostMapping("/deleteClassItem")
    public ResponseEntity<?> deleteProjectClass(@RequestBody List<Map<String, Long>> classDeleteReq) {
        classItemService.deleteProjectClass(classDeleteReq);
        return new ResponseEntity<>(new ResponseDto<>(200, "클래스 삭제 성공", classDeleteReq), HttpStatus.OK);
    }

//    /* 클래스파일 추출 */
//    @GetMapping("/createJsonFile")
//    public ResponseEntity<?> createFile(@RequestPart(value = "classId", required = false) @Valid List<Map<String, Long>> param) throws Exception{
//        classItemService.createFile(param);
//        return new ResponseEntity<>(new ResponseDto<>(200, "파일생성 성공", null), HttpStatus.OK);
//    }
}