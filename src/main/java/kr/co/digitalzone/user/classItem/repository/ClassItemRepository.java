package kr.co.digitalzone.user.classItem.repository;

import kr.co.digitalzone.user.classItem.domain.ClassItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

import static kr.co.digitalzone.user.classItem.response.ClassItemRespDto.ClassItemListResponse;

@Repository
public interface ClassItemRepository extends JpaRepository<ClassItem, Long> {
    /* 클래스 조회 */
    @Query("select c from AIL_CLASS_ITEM c " +
            "where c.delYn = 'N' "
            )
    List<ClassItemListResponse> findAll(Long classId);

    /* 클래스 삭제 일괄처리 */
    @Modifying(clearAutomatically = true) // clearAutomatically 영속성 초기화
    @Query("update AIL_CLASS_ITEM p set p.delYn = 'Y', p.updateAt = local_datetime where p.classId in (:classIdList)")
    void deleteClass(@Param("classIdList") List<Long> classIdList);

    /* 클래스 반환데이터 조회 */
    @Query("select c from AIL_CLASS_ITEM c where c.classId =:classId ")
    ClassItem findAllById(@Param("classId") Long classId);

    /* 동일한 Email 과 className을 가진 클래스가 존재하는지 확인하는 메서드 */
    boolean existsByEmailAndClassNameAndDelYn(String email, String className, String delYn);
}