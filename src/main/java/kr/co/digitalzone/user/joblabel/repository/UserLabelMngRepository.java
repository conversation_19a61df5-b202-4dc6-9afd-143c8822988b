package kr.co.digitalzone.user.joblabel.repository;

import kr.co.digitalzone.user.jobItem.domain.JobLabel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserLabelMngRepository extends JpaRepository<JobLabel, Long> {
    @Modifying
    @Query("update AIL_JOB_LABEL " +
            "set delYn = 'Y' " +
            "where projectFile.fileId in (:fileId) ")
    public void resetLabel(@Param("fileId") List<Long> jobIdList);

    @Modifying
    @Query("update AIL_JOB_LABEL " +
            "set delYn = 'Y' " +
            "where projectFile.fileId = (:fileId) ")
    public void resetLabelInfo(@Param("fileId") Long fileId);
}
