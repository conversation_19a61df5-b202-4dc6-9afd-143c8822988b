package kr.co.digitalzone.user.common.config;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Optional;

@Configuration
public class AuditConfig implements AuditorAware<String> {
    // 생성자, 수정자의 data값 설정을 위해 만듬
    @Override
    public Optional<String> getCurrentAuditor() {
        HttpServletRequest request =
                ((ServletRequestAttributes)
                        RequestContextHolder.currentRequestAttributes()).getRequest();
        String email = request.getHeader("email"); // 헤더에서 id 정보를 가져와서 리턴해줌
        return Optional.of(email);
    }
}
