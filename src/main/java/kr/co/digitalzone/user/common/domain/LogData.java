package kr.co.digitalzone.user.common.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import java.time.LocalDateTime;

@Getter
@SequenceGenerator(
        name = "LOG_NO",
        sequenceName = "seq_log_data_no",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_LOG_DATA")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class LogData {
    /* 로그번호 PK */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE, // 사용할 전략을 시퀀스로 선택 SEQUENCE
            generator="LOG_NO"                // 식별자 생성기를 설정해놓은 OPERT_NO 설정
    )
    @Column(name = "LOG_NO")
    private Long logNo;

//    /* 회사코드 */
//    @NotNull
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 로그유형 */
    @NotNull
    @Column(name = "TYPE", length = 15)
    private String type;

    /* 년 */
    @NotNull
    @Column(name = "YEAR")
    private Long year;

    /* 월 */
    @NotNull
    @Column(name = "MONTH")
    private Long month;

    /* 일 */
    @NotNull
    @Column(name = "DAY")
    private Long day;

    /* 시간 */
    @NotNull
    @Column(name = "HOUR")
    private Long hour;

    /* 데이터 */
    @NotNull
    @Column(name = "LOGDATA", columnDefinition="TEXT")
    private String logdata;


    /* 등록일시 */
    @CreatedDate
    @NotNull
    @Column(name = "REGISTERED_AT", nullable = false, updatable = false)
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime registeredAt;

    /* 프로세스ID */
    @NotNull
    @CreatedBy
    @Column(name = "REGISTERED_BY", length = 36)
    private String registeredBy;

    @Builder
    public LogData(Long logNo,String type, Long year, Long month, Long day, Long hour, String logdata, LocalDateTime registeredAt, String registeredBy) {
        this.logNo = logNo;
//        this.ccode = ccode;
        this.type = type;
        this.year = year;
        this.month = month;
        this.day = day;
        this.hour = hour;
        this.logdata = logdata;
        this.registeredAt = registeredAt;
        this.registeredBy = registeredBy;
    }
}
