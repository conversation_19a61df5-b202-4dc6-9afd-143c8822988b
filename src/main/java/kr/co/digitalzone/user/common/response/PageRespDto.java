package kr.co.digitalzone.user.common.response;

import lombok.Data;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data

public class PageRespDto<T> {
    // 페이징 content
    private List<T> content;
    // 총 페이지 번호
    private int totalPage;
    // 현재 페이지 번호
    private int page;
    // 목록 사이즈
    private int size;
    // 시작페이지,끝페이지 번호
    private int start, end;
    // 이전, 다음
    private boolean prev, next;
    // 페이지 번호 목록 리스트
    private List<Integer> pageList;
    // Function<EN,DTO> : 엔티티 객체들을 DTO로 변환해주는 기능
    public PageRespDto(Pageable pageable, Page<T> pageInfo){
//    public PageRespDto(Page<EN> result, Function<EN,DTO> fn){

//        dtoList = result.stream().map(fn).collect(Collectors.toList());
//
//        totalPage = result.getTotalPages();
//
//        makePageList(result.getPageable());
        content = pageInfo.getContent();
        totalPage = pageInfo.getTotalPages();	   // 페이지 총 갯수
        makePageList(pageable);
    }

    private void makePageList(Pageable pageable) {
        this.page = pageable.getPageNumber() + 1 ; // 0부터 시작하므로 1을 더해준다
        this.size = pageable.getPageSize();

        // temp end page
        // 끝번호를 미리 계산하는 이유 : 시작번호 계산 수월하게 하기위해
        // * 끝 번호 구하는 공식
//        int tempEnd = (int)(Math.ceil(page / 10.0)) * 10;
        int tempEnd = (int) (Math.ceil(page / (double) size)) * size;
        int start = tempEnd - (size - 1);
        int end = Math.min(totalPage, tempEnd);
        boolean prev = start > 1;
        boolean next = totalPage > tempEnd;
//        start = tempEnd - 9; // 화면에 10페이지씩 보여준다면 시작번호는 무조건 끝번호에서 9를 뺀 값이다
//
//        prev = start > 1;
//
//        end = totalPage > tempEnd ? tempEnd : totalPage;
//
//        next = totalPage > tempEnd;

        pageList = IntStream.rangeClosed(start,end).boxed().collect(Collectors.toList());
    }
}
