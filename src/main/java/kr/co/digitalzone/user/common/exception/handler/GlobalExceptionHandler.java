package kr.co.digitalzone.user.common.exception.handler;


import jakarta.validation.ConstraintViolationException;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.user.common.exception.errorCode.CommonErrorCode;
import kr.co.digitalzone.user.common.exception.errorCode.ErrorCode;
import kr.co.digitalzone.user.common.exception.exception.RestApiException;
import kr.co.digitalzone.user.common.exception.exception.VaildationException;
import kr.co.digitalzone.user.common.exception.response.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.IOException;
import java.sql.SQLException;

import static kr.co.digitalzone.user.common.exception.errorCode.CommonErrorCode.*;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    // RuntimeException 처리
    @ExceptionHandler(RestApiException.class)
    public ResponseEntity<Object> handleCustomException(RestApiException e) {
        return handleExceptionInternal(e.getErrorCode());
    }

    // IllegalArgumentException 에러 처리
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Object> handleIllegalArgument(IllegalArgumentException e) {
        log.warn("handleIllegalArgument", e);
        return handleExceptionInternal(INVALID_PARAMETER, e.getMessage());
    }

    // NoHandlerFoundException 404 에러 처리
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<Object> handleNoHandlerFound(NoHandlerFoundException e) {
        log.warn("NoHandlerFoundException", e);
        return handleExceptionInternal(RESOURCE_NOT_FOUND, RESOURCE_NOT_FOUND.getMessage());
    }

    // 대부분의 에러 처리
//    @ExceptionHandler({Exception.class})
//    public ResponseEntity<Object> handleAllException(Exception ex) {
//        log.warn("handleAllException", ex);
//        return handleExceptionInternal(INTERNAL_SERVER_ERROR);
//    }

    // RuntimeException과 대부분의 에러 처리 메세지를 보내기 위한 메소드
    private ResponseEntity<Object> handleExceptionInternal(ErrorCode errorCode) {
        return ResponseEntity.status(errorCode.getHttpStatus())
                .body(makeErrorResponse(errorCode));
    }

    // 코드 가독성을 위해 에러 처리 메세지를 만드는 메소드 분리
    private ErrorResponse makeErrorResponse(ErrorCode errorCode) {
        return ErrorResponse.builder()
                .code(errorCode.name())
                .message(errorCode.getMessage())
                .build();
    }

    private ResponseEntity<Object> handleExceptionInternal(ErrorCode errorCode, String message) {
        return ResponseEntity.status(errorCode.getHttpStatus())
                .body(makeErrorResponse(errorCode, message));
    }

    // 코드 가독성을 위해 에러 처리 메세지를 만드는 메소드 분리
    private ErrorResponse makeErrorResponse(ErrorCode errorCode, String message) {
        return ErrorResponse.builder()
                .code(String.valueOf(errorCode.getHttpStatus().value()))
                .message(message)
                .build();
    }

    // 벨리데이션
    @ExceptionHandler(VaildationException.class)
    public ResponseEntity<?> apiException(VaildationException e) {
        log.error(e.getMessage());
        return new ResponseEntity<>(new ResponseDto(-1, e.getMessage(), e.getErrorMap()), HttpStatus.BAD_REQUEST);
    }

    // NullPointerException 처리
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<Object> handleNullPointerException(NullPointerException ex) {
        log.warn("handleNullPointerException", ex);
        return handleExceptionInternal(INTERNAL_SERVER_ERROR, "Null pointer exception occurred");
    }

    // IllegalStateException 처리
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<Object> handleIllegalStateException(IllegalStateException ex) {
        log.warn("handleIllegalStateException", ex);
        return handleExceptionInternal(INTERNAL_SERVER_ERROR, ex.getMessage());
    }

    // IOException 처리
    @ExceptionHandler(IOException.class)
    public ResponseEntity<Object> handleIOException(IOException ex) {
        log.warn("handleIOException", ex);
        return handleExceptionInternal(INTERNAL_SERVER_ERROR, "IO exception occurred");
    }

    // SQLException 처리
    @ExceptionHandler(SQLException.class)
    public ResponseEntity<Object> handleSQLException(SQLException ex) {
        log.warn("handleSQLException", ex);
        return handleExceptionInternal(INTERNAL_SERVER_ERROR, "SQL exception occurred");
    }

    // 벨리데이션
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> methodArgNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(makeErrorResponse(CommonErrorCode.INVALID_PARAMETER, e.getMessage()));
    }
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<?> ConstraintViolationException(ConstraintViolationException e) {
        log.error(e.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(makeErrorResponse(CommonErrorCode.INVALID_PARAMETER, e.getMessage()));
    }

    // 대부분의 에러 처리
    @ExceptionHandler({Exception.class})
    public ResponseEntity<Object> handleAllException(Exception ex) {
        log.warn("handleAllException", ex);
        return handleExceptionInternal(INTERNAL_SERVER_ERROR, ex.getMessage());
    }
}
