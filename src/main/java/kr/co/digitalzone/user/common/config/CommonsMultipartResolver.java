package kr.co.digitalzone.user.common.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class CommonsMultipartResolver {
    /// WebMVCConfiguration.java
//    @Bean
//    public CommonsMultipartResolver multipartResolver(){
//        CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
//        commonsMultipartResolver.setDefaultEncoding("UTF-8");
//        commonsMultipartResolver.setMaxUploadSize(50 * 1024 * 1024);
//        return commonsMultipartResolver;
//    }
}
