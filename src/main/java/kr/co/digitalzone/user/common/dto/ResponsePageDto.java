package kr.co.digitalzone.user.common.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.List;

@RequiredArgsConstructor
@Getter
public class ResponsePageDto<T> {
    private int code;
    private String message;

    private int lists;

    private int totalPage;

    private Long totalCount;

    private int currentPage;

    private List<PageImpl> data;

    public ResponsePageDto(int code, String message, Page<T> data) {
        this.code = code;
        this.message = message;
        this.data = (List<PageImpl>) data.getContent();
        this.totalCount = data.getTotalElements();
        this.lists = data.getSize();
        this.totalPage = data.getTotalPages();
        this.currentPage = data.getPageable().getPageNumber() + 1;
    }
}
