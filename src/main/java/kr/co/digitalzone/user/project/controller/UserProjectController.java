package kr.co.digitalzone.user.project.controller;

import com.querydsl.core.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.user.classItem.service.ClassItemService;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import kr.co.digitalzone.user.common.dto.ResponsePageDto;
import kr.co.digitalzone.user.common.request.SearchRequest;
import kr.co.digitalzone.user.project.domain.Project;
import kr.co.digitalzone.user.project.request.UserProjectReqDto.*;
import kr.co.digitalzone.user.project.response.UserProjectRespDto;
import kr.co.digitalzone.user.project.response.UserProjectRespDto.*;
import kr.co.digitalzone.user.project.service.UserProjectService;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Validated
@Tag(name="프로젝트 관련 API",description = "프로젝트 생성,삭제,조회,종료 API")
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class UserProjectController {

    private final UserProjectService userProjectService;
    private final ClassItemService classItemService;

    /* 프로젝트 생성 */
    @Operation(summary = "프로젝트 생성", description = "프로젝트 관련 API")
    @PostMapping(value = "/saveProject", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
//            {MediaType.APPLICATION_JSON_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE})
    public ResponseEntity<?> save(@RequestPart(value = "projects") @Valid ProjectSaveRequest projectSaveReq,
                                  @RequestPart(value = "files") MultipartFile[] files,
                                  @RequestPart(value = "classItems") @Valid List<Map<String, Long>> classReq) {

        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();

        // Token 안에 있는 email 가져오기
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String email = authentication.getName();
        projectSaveReq.setEmail(email);
        ProjectSaveResponse result = null;

        try {
            // 동일한 email과 projectName을 가진 프로젝트가 존재하는지 확인 (del_yn이 'N'인 경우만)
            boolean exists = userProjectService.existsByUserEmailAndProjectNameAndDelYn(email, projectSaveReq.getProjectName(), "N");
            if (!exists) { // 동일한 email과 projectName을 가진 프로젝트가 존재하지 않는 경우
                result = userProjectService.save(projectSaveReq, files, classReq);
                code = ApiResponseEnum.OK.getCode();
                message = "프로젝트 생성 성공";
            } else {
                throw new IllegalStateException(ApiResponseEnum.DUPLICATE_PROJECTNAME.getMessage());
            }
        } catch (Exception e) {
            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_PROJECTNAME.getMessage())) {
                code = ApiResponseEnum.DUPLICATE_PROJECTNAME.getCode();
                message = ApiResponseEnum.DUPLICATE_PROJECTNAME.getMessage();
//            } else if (e.getMessage().equals("중복파일명입니다")) {
//                code = ApiResponseEnum.DUPLICATE_FILENAME.getCode();
//                message = ApiResponseEnum.DUPLICATE_FILENAME.getMessage();
            }
            e.getStackTrace();
        }
        return new ResponseEntity<>(new ResponseDto<>(code, message, result), HttpStatus.OK);
    }

    /* 프로젝트내역 조회 */
    @Operation(summary="프로젝트 전체 내역 조회", description = "프로젝트 탭 누르면 프로젝트 작업목록을 조회")
    @GetMapping("/findProject")
    public ResponseEntity<?> findProject(@RequestHeader(value = "email") String email, @RequestParam(value="jobType", required = false) String jobType,@RequestParam(value="status", required = false) String status,
                                         @PageableDefault(page = 0,size = 10, sort = "id", direction = Sort.Direction.DESC) Pageable pageable, SearchRequest searchRequest) {

        Page<UserProjectResponse> result = userProjectService.findAll(email, jobType, status, pageable, searchRequest);

        return new ResponseEntity<>(new ResponsePageDto<>(200, "프로젝트 전체 내역 조회 성공", result), HttpStatus.OK);
    }

    /* 프로젝트 삭제 */
    @Operation(summary= "프로젝트 삭제", description = "프로젝트 삭제 API")
    @PostMapping("/deleteProject")
    public ResponseEntity<?> deleteProject(@RequestBody List<Map<String, Long>> projectDeleteReq){
        userProjectService.deleteById(projectDeleteReq);
        String result = "";

        return new ResponseEntity<>(new ResponseDto<>(200, "프로젝트 삭제 성공", null), HttpStatus.OK);
    }

    /* 프로젝트/작업목록 조회 */
    @Operation(summary = "프로젝트 세부페이지 접속", description = "프로젝트 전체목록에서 하나의 프로젝트로 클릭하여 접속 (백엔드API 활용하고 있음)")
        @GetMapping("/projectDetail")
    public ResponseEntity<?> projectDetail(@RequestHeader(value = "email") String email, @RequestParam("projectId") Long projectId) {
        UserJobResponse result = userProjectService.findJob(email, projectId);

        return new ResponseEntity<>(new ResponseDto<>(200, "프로젝트 정보 조회 성공", result), HttpStatus.OK);
    }

    @GetMapping("/projectProgress")
    @Operation(summary= "작업별 진행률", description = "시각화에 대한 정보 조회")
    public ResponseEntity<?> projectProgressInfo(@RequestHeader(value = "email") String email,
                                           @RequestParam("projectId") Long projectId) {

        ProjectProgressResponse result = userProjectService.getProgressInfo(projectId);

        return new ResponseEntity<>(new ResponseDto<>(200, "작업현황 조회 성공", result), HttpStatus.OK);
    }

    /* 프로젝트 종료 */
    @GetMapping("/expireProject")
    @Operation(summary="프로젝트 종료", description = "프로젝트 종료 시, 종료 설정일이 만료일로 변경")
    public ResponseEntity<?> expireProject(@RequestParam("projectId") Long projectId) {
        userProjectService.expireProject(projectId);

        return new ResponseEntity<>(new ResponseDto<>(200, "프로젝트 종료 성공", null), HttpStatus.OK);
    }
}
