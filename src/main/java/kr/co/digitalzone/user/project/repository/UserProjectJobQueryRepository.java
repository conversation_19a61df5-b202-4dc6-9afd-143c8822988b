package kr.co.digitalzone.user.project.repository;

import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_JobBlurResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_JobItemResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_JobProgressResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.project.domain.QProject.project;
import static kr.co.digitalzone.user.project.response.UserProjectRespDto.*;


@Repository
@RequiredArgsConstructor
public class UserProjectJobQueryRepository {
    private final EntityManager em;

//    /* 작업목록 내역 조회 */
//    public Page<JobItemResponse> findJobList(String email, Long projectId, String keyword, Pageable pageable) {
//        JPAQueryFactory query = new JPAQueryFactory(em);
//
//        List<JobItemResponse> content = query.selectDistinct(
//                        new QUserProjectRespDto_JobItemResponse(
//                                project.projectId,
//                                project.projectName,
//                                project.registeredAt,
//                                project.endAt,
//                                ExpressionUtils.as(
//                                        JPAExpressions.select(projectFile.fileId.count())
//                                            .from(projectFile)
//                                            .where(
//                                                    projectFile.project.projectId.eq(project.projectId),
////                                                    projectFile.ccode.eq(ccode),
//                                                    projectFile.delYn.eq("N")
////                                                    , projectFile.jobItem.jobId.eq(jobItem.jobId)
//                                            ), "fileCount")
//                        )
//                )
//                .from(project)
//                .where(
////                        project.ccode.eq(ccode),
//                        project.delYn.eq("N"),
//                        project.projectId.eq(projectId)
//                )
//                .offset(pageable.getOffset())
//                .limit(pageable.getPageSize())
//                .fetch();
//
//        JPAQuery<Long> countQuery = query
//                .select(project.projectId.count())
//                .from(project)
//                .where(
////                        project.ccode.eq(ccode),
//                        project.delYn.eq("N"),
//                        project.projectId.eq(projectId)
//                );
//
//        List<JobItemResponse> collect = content.stream().map(item -> {
//            JobBlurResponse userJobBlurResponse = blurCount(item.getProjectId());
//            JobProgressResponse userJobProgressResponse = progressCount(item.getProjectId());
//
//            setBlur(userJobBlurResponse, item);
//
//            setProgress(userJobProgressResponse, item);
//
//            return item;
//        }).collect(Collectors.toList());
//
//        return PageableExecutionUtils.getPage(collect, pageable, countQuery::fetchCount);
////        return PageableExecutionUtils.getPage(content, pageable, countQuery::fetchCount);
//    }

    // 비식별화,라벨링 수
    private void setBlur(JobBlurResponse data, JobItemResponse item) {
        Integer blurCount = data.getBlurCount();
        if (!StringUtils.isEmpty(blurCount)) {
            item.setBlurCount(Long.valueOf(blurCount));
        } else {
            item.setBlurCount(0L);
        }

        Integer labelCount = data.getLabelCount();
        if (!StringUtils.isEmpty(labelCount)) {
            item.setLabelCount(Long.valueOf(labelCount));
        } else {
            item.setLabelCount(0L);
        }
    }

    // 진행률
    private void setProgress(JobProgressResponse data, JobItemResponse item) {
        Long fileCount = data.getFileCount();
        Long fileTotalCount = data.getFileTotalCount();

        if (fileTotalCount > 0 && fileCount > 0) {
            Double progress = ((double) fileCount / (double) fileTotalCount) * 100.0;
            item.setProgress(String.format("%d", Math.round(progress)));
        } else {
            item.setProgress("0");
        }
    }

    /* 비식별화, 라벨수 */
    public JobBlurResponse blurCount(Long projectId) {
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(
                        new QUserProjectRespDto_JobBlurResponse(
                                new CaseBuilder()
                                        .when(projectFile.fileBluring.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum() ,
                                new CaseBuilder()
                                        .when(projectFile.fileLabeling.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum()
                        )
                )
                .from(projectFile)
                .where(
//                        projectFile.ccode.eq(ccode),
                        projectFile.delYn.eq("N"),
                        projectFile.project.projectId.eq(projectId)
                )
                .fetchOne();
    }

}