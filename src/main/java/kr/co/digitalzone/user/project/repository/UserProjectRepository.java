package kr.co.digitalzone.user.project.repository;

import kr.co.digitalzone.user.project.domain.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserProjectRepository extends JpaRepository<Project, Long> {
    /* 프로젝트 삭제 */
    @Modifying(clearAutomatically = true)
    @Query("update AIL_PROJECT p set p.delYn = 'Y', p.updateAt = local_datetime where p.projectId in (:projectId)")
    int deleteProject(@Param("projectId") List<Long> projectIdList);



    /* 프로젝트 반환데이터 조회 */
    @Query("select p from AIL_PROJECT p where p.projectId =:projectId ")
    Project findAllById(@Param("projectId") Long projectId);


    // 동일한 email과 projectName을 가진 프로젝트가 존재하는지 확인하는 메서드 (del_yn이 'N'인 경우만)
    boolean existsByUserEmailAndProjectNameAndDelYn(String userEmail, String projectName, String delYn);
}

