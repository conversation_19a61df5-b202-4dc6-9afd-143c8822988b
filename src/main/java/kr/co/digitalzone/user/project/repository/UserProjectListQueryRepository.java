package kr.co.digitalzone.user.project.repository;

import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_UserProgressResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_UserProjectResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_UserProjectblurResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.project.domain.QProject.project;
import static kr.co.digitalzone.user.project.response.UserProjectRespDto.*;

@Repository
@RequiredArgsConstructor
public class UserProjectListQueryRepository {
    private final EntityManager em;

    /* 프로젝트 조회 */
    public Page<UserProjectResponse> findProject(String email, String jobType, String status, Pageable pageable, String keyword) {
        JPAQueryFactory query = new JPAQueryFactory(em);

        // 컬럼 정렬 list
        List<OrderSpecifier> orderList = sortCond(pageable);

        List<UserProjectResponse> content = query.selectDistinct(
                        new QUserProjectRespDto_UserProjectResponse(
                                project.projectId,
                                project.projectName,
                                project.registeredAt,
                                project.endAt,
                                new CaseBuilder()
                                        .when(project.projectStatus.eq("P"))
                                        .then("진행중")
                                        .otherwise("종료")
                                ,project.blurYn
                        )
                )
                .from(project)
                .where(
//                        project.ccode.eq(ccode),
                        project.delYn.eq("N"),
                        project.userEmail.eq(email),
                        project.projectName.contains(keyword),
                        jobType(jobType),
                        status(status)
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(orderList.stream().toArray(OrderSpecifier[]::new))
                .fetch();

        JPAQuery<Long> countQuery = query
                .select(project.projectId.count())
                .from(project)
                .where(
//                        project.ccode.eq(ccode),
                        project.delYn.eq("N"),
                        project.userEmail.eq(email),
                        project.projectName.contains(keyword),
                        jobType(jobType),
                        status(status)
                );

        List<UserProjectResponse> collect = content.stream().map(item -> {
            UserProjectblurResponse projectblurResponse = blurCount(item.getProjectId());
            UserProgressResponse progressResponse = progressCount(item.getProjectId());

            setBlur(projectblurResponse, item);

            setProgress(progressResponse, item);

            return item;
        }).collect(Collectors.toList());

        return PageableExecutionUtils.getPage(collect, pageable, countQuery::fetchCount);
    }

    private List<OrderSpecifier> sortCond(Pageable pageable){
        List<OrderSpecifier> orderList = new ArrayList<>();

        pageable.getSort().stream().forEach(order -> {
            Order direction = order.isAscending() ? Order.ASC : Order.DESC;
            String prop = order.getProperty();
            Path<Object> fieldPath = Expressions.path(Object.class, project, prop);
            orderList.add(new OrderSpecifier(direction, fieldPath));
        });
        return orderList;
    }

    private BooleanExpression jobType(String jobType) {
        //// 라벨링 작업은 필수, 블러 작업은 선택
        return "labeling".equals(jobType) ? project.blurYn.eq("N") : "blur".equals(jobType)? project.blurYn.eq("Y"):null;
    }

    private BooleanExpression status(String status) {
        //// P : 진행중, C : 완료
        return "p".equalsIgnoreCase(status) ? project.projectStatus.eq("P") : "c".equalsIgnoreCase(status) ? project.projectStatus.eq("C"):null;
    }

    // 비식별화,라벨링 수
    private void setBlur(UserProjectblurResponse data, UserProjectResponse item) {
        Integer blurCount = data.getBlurCount();
        if (!StringUtils.isEmpty(blurCount)) {
            item.setBlurCount(Long.valueOf(blurCount));
        } else {
            item.setBlurCount(0L);
        }

        Integer labelCount = data.getLabelCount();
        if (!StringUtils.isEmpty(labelCount)) {
            item.setLabelCount(Long.valueOf(labelCount));
        } else {
            item.setLabelCount(0L);
        }
    }

    // 진행률
    private void setProgress(UserProgressResponse data, UserProjectResponse item) {
        Long fileCount = data.getFileCount();
        Long fileTotalCount = data.getFileTotalCount();

        if (fileTotalCount > 0 && fileCount > 0) {
            Double progress = ((double) fileCount / (double) fileTotalCount) * 100.0;
            item.setProgress(String.format("%d", Math.round(progress)));
        } else {
            item.setProgress("0");
        }

        item.setFileCount(fileTotalCount  != null ? fileTotalCount : 0);
    }


    /* 비식별화, 라벨수 */
    public UserProjectblurResponse blurCount(Long projectId) {
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.select(
                        new QUserProjectRespDto_UserProjectblurResponse(
                                new CaseBuilder()
                                        .when(projectFile.fileBluring.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum() ,
                                new CaseBuilder()
                                        .when(projectFile.fileLabeling.eq("Y"))
                                        .then(1)
                                        .otherwise(0).sum()
                        )
                )
                .from(projectFile)
                .where(
                        projectFile.delYn.eq("N") ,
                        projectFile.project.projectId.eq(projectId)
                )
                .fetchOne();
    }

    /* 진행률 */
    public UserProgressResponse progressCount(Long projectId) {
        JPAQueryFactory query = new JPAQueryFactory(em);

        return query.select(
                        new QUserProjectRespDto_UserProgressResponse(
                                projectFile.count().as("fileTotalCount"),
                                new CaseBuilder()
                                        .when(projectFile.fileStatus.eq("E"))
                                        .then(1)
                                        .otherwise(0).sum().longValue().as("fileCount")
                        ))
                .from(projectFile)
                .where(
                        projectFile.delYn.eq("N"),
                        projectFile.project.projectId.eq(projectId)
                )
                .fetchOne();
    }
}