package kr.co.digitalzone.user.project.service;

import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.common.request.SearchRequest;
import kr.co.digitalzone.user.file.domain.ProjectFile;
import kr.co.digitalzone.user.file.repository.FileRepository;
import kr.co.digitalzone.user.project.domain.Project;
import kr.co.digitalzone.user.project.request.UserProjectReqDto;
import kr.co.digitalzone.user.project.request.UserProjectReqDto.*;
import kr.co.digitalzone.user.classItem.service.ProjectClassService;
import kr.co.digitalzone.user.file.service.UserFileService;
import kr.co.digitalzone.user.project.repository.*;
import kr.co.digitalzone.user.project.response.UserProjectRespDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static kr.co.digitalzone.user.project.response.UserProjectRespDto.*;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = false)
@Slf4j
public class UserProjectService {
    private final UserProjectRepository userProjectRepository;
    private final UserProjectQueryRepository userProjectQueryRepository;
    private final UserProjectListQueryRepository userProjectListRepository;
    private final UserProjectJobQueryRepository userProjectJobQueryRepository;
    private final UserFileService fileService;
    private final ProjectClassService projectClassService;
    private final EntityManager em;
    private final FileRepository fileRepository;

    // 동일한 email과 projectName을 가진 프로젝트가 존재하는지 확인하는 메서드 (del_yn이 'N'인 경우만)
    public boolean existsByUserEmailAndProjectNameAndDelYn(String email, String projectName, String delYn) {
        return userProjectRepository.existsByUserEmailAndProjectNameAndDelYn(email, projectName, delYn);
    }

    /* 프로젝트 생성 */
    @Transactional
    public ProjectSaveResponse save(ProjectSaveRequest projectSaveReq, MultipartFile[] files,
                                                       List<Map<String, Long>> classReq) {

        Project project = Project.builder()
//                .ccode(projectSaveReq.getCcode())
                .projectName(projectSaveReq.getProjectName())
                .projectCmt(projectSaveReq.getProjectCmt())
                .projectStatus("P")
                .blurYn(projectSaveReq.getBlurYn())
//                .autoLabelYn(projectSaveReq.getAutoLabelYn())
                .delYn("N")
                .endAt(projectSaveReq.getEndAt())
                .endBy(projectSaveReq.getEndBy())
//                .startAt(projectSaveReq.getStartAt())
                .userEmail(projectSaveReq.getEmail()) // project table email not null
//                .userEmail("<EMAIL>") // project table email not null
                .build();
        userProjectRepository.save(project);
        em.flush();


        // 파일 생성
        if (files != null) {
           fileService.uploadFile(files, project);
        }
        // 클래스링크 생성
        if (classReq != null) {
            projectClassService.save(classReq, project);
        }

        Project projects = userProjectRepository.findAllById(project.getProjectId());

       return new ProjectSaveResponse(projects);
    }


    /* 프로젝트 삭제 */
    @Transactional
    public void deleteById(List<Map<String, Long>> projectDeleteReq){
        List<Long> projectIdList = projectDeleteReq
                .stream()
                .map(item -> item.get("projectId")).collect(Collectors.toList());

        userProjectRepository.deleteProject(projectIdList);
    }

    /* 프로젝트 내역 */
    public Page<UserProjectResponse> findAll(String email, String jobType, String status, Pageable pageable, SearchRequest searchRequest) {
        String keyword = searchRequest.getKeyword();
        return userProjectListRepository.findProject(email, jobType, status, pageable, keyword);
    }

    /* 프로젝트/작업목록 프로젝트 정보조회 */
    public UserJobResponse findJob(String email, Long projectId) {
        return userProjectQueryRepository.findById(email, projectId);
    }

    /* 프로젝트/작업목록 작업 리스트 조회 */
//    public List<UserJobInterface> findJobAll(String ccode, String email, Long projectId, SearchRequest searchRequest) {
//        String keyword = searchRequest.getKeyword();
//        return userProjectRepository.findJobAll(ccode, email, projectId, keyword);
//    }

    public UserProjectRespDto.ProjectProgressResponse getProgressInfo(Long projectId){
        return userProjectQueryRepository.getProjectProgress(projectId);
    }

//    public Page<UserProjectRespDto.JobItemResponse> findJobAll(String email, Long projectId, SearchRequest searchRequest, Pageable pageable) {
//        String keyword = searchRequest.getKeyword();
//        return userProjectJobQueryRepository.findJobList(email, projectId, keyword, pageable);
//    }

    /* 프로젝트 종료 */
    public void expireProject(Long projectId){
        Project project = userProjectRepository.findById(projectId).orElseThrow(()->new IllegalStateException("존재하지않는 프로젝트 입니다."));
        userProjectQueryRepository.expireProject(projectId);
    }
//    // 파일명 중복체크
//    public boolean existsByFileName(String fileName) {
//        return fileRepository.existsByFileName(fileName);
//    }
//    /* 파일명 중복체크*/
//    public boolean existsByFileName(String fileName) {
//        return userProjectRepository.existsByFileName(fileName);
//    }

}