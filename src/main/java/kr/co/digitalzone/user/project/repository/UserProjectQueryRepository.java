package kr.co.digitalzone.user.project.repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_ProjectProgressResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_UserJobResponse;
import kr.co.digitalzone.user.project.response.QUserProjectRespDto_UserProgressResponse;
import kr.co.digitalzone.user.project.response.UserProjectRespDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
import static kr.co.digitalzone.user.project.domain.QProject.project;
import static kr.co.digitalzone.user.project.response.UserProjectRespDto.*;

@Repository
@RequiredArgsConstructor
public class UserProjectQueryRepository {
    private final EntityManager em;
    public UserJobResponse findById(String email, Long projectId) {

        JPAQueryFactory query = new JPAQueryFactory(em);

        return query.select(
                        new QUserProjectRespDto_UserJobResponse(
                                project.projectId
                                , project.projectName
                                , project.projectCmt
                                , project.endAt
                                , project.registeredAt
                                , project.blurYn
                                ,project.projectStatus
                        )
                ).distinct()
                .from(project)
                .where(
                        project.projectId.eq(projectId),
                        project.delYn.eq("N")
                )
                .fetchOne();
    }

    public ProjectProgressResponse getProjectProgress(long projectId){

        JPAQueryFactory query = new JPAQueryFactory(em);

        // 비식별화 완료 and 라벨링 작업 미완료 조건
        BooleanBuilder bb = new BooleanBuilder();
        bb.and(projectFile.fileLabeling.eq("N")).and(projectFile.fileBluring.eq("Y"));

        ProjectProgressResponse ppr = query.select(
                        new QUserProjectRespDto_ProjectProgressResponse(
                                new CaseBuilder()
                                        .when(projectFile.fileBluring.eq("N"))
                                        .then(1)
                                        .otherwise(0).sum()
                                , new CaseBuilder()
                                .when(bb)
                                .then(1)
                                .otherwise(0).sum()

//                        , new CaseBuilder()
//                            .when(projectFile.fileLabeling.eq("Y"))
//                            .then(1)
//                            .otherwise(0).sum()
                        , new CaseBuilder()
                        .when(projectFile.fileStatus.eq("E"))
                        .then(1)
                        .otherwise(0).sum()
                        , projectFile.count()
                        , project.blurUpdateAt
                        , project.labelUpdateAt
                        , project.compUpdateAt
                    )
                )
                .from(projectFile)
                .innerJoin(project)
                .on(projectFile.project.projectId.eq(project.projectId))
                .where(projectFile.project.projectId.eq(projectId))
                .groupBy(project.projectId)
                .fetchOne();

        setProgress(ppr);

        return ppr;
    }

    /* 프로젝트 종료 */
    @Transactional
    public void expireProject(Long projectId){
        JPAQueryFactory query = new JPAQueryFactory(em);
        LocalDateTime now = LocalDateTime.now();
        long result = query.update(project)
                .set(project.projectStatus, "C")
                .set(project.endAt, now.format(DateTimeFormatter.ISO_DATE))
                .where(project.projectId.eq(projectId))
                .execute();
    }

    // 진행률
    private void setProgress(ProjectProgressResponse data) {
        Long totalCount = data.getTotalCnt();
        int blurCount = data.getBlurCnt();
        int labelCount = data.getLabelCnt();
        int compCount = data.getCompCnt();

        if (totalCount > 0 ) {

            long blurPer = Math.round(((double) blurCount / (double) totalCount) * 100.0);
            long labelPer = Math.round(((double) labelCount / (double) totalCount) * 100.0);
            long compPer = Math.round(((double) compCount / (double) totalCount) * 100.0);

            data.setBlurPer(String.valueOf(blurPer));

            data.setLabelPer(String.valueOf(labelPer));

            data.setCompPer(String.valueOf(compPer));


        } else {
            data.setBlurPer("0");
            data.setLabelPer("0");
            data.setCompPer("0");
        }
    }
}