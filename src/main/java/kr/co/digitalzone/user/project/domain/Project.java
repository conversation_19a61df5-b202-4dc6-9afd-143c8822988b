package kr.co.digitalzone.user.project.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import kr.co.digitalzone.user.classItem.domain.ProjectClass;
import kr.co.digitalzone.user.common.domain.CommonEntity;
import kr.co.digitalzone.user.file.domain.ProjectFile;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@SequenceGenerator(
        name = "PROJECT_ID",
        sequenceName = "seq_project_id",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_PROJECT")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class Project extends CommonEntity {
    /* 프로젝트번호 */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE,
            generator="PROJECT_ID"
    )
    @Column(name = "PROJECT_ID")
    private Long projectId;

//    /* 회사코드 */
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 프로젝트명 */
    @NotNull
    @Column(name = "PROJECT_NAME", nullable = false)
    private String projectName;

    /* 프로젝트설명 */
    @NotNull
    @Column(name = "PROJECT_CMT")
    private String projectCmt;

    /* 프로젝트상태 */
    @Column(name = "PROJECT_STATUS", length = 5)
    private String projectStatus;

    /* 비식별화여부 */
    @Column(name = "BLUR_YN", length = 5)
    private String blurYn;
//
//    /* 오토라벨여부 */
//    @NotNull
//    @Column(name = "AUTO_LABEL_YN", length = 5)
//    private String autoLabelYn;

    /* 삭제여부 */
    @NotNull
    @Column(name = "DEL_YN", length = 5)
    private String delYn;

    /* 종료일시 */
    @NotNull
    @Column(name = "END_AT", length = 30)
    private String endAt;
    
    /* 종료자아이디 */
    @NotNull
    @Column(name = "END_BY", length = 30)
    private String endBy;

    /* 로그인이메일 */
    @NotNull
    @Column(name = "USER_EMAIL", length = 100)
    private String userEmail;

    /* 최종 labeling 작업일시 */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    @Column(name = "LABEL_UPDATED_AT")
    private LocalDateTime labelUpdateAt;

    /* 최종 blur 작업일시 */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    @Column(name = "BLUR_UPDATED_AT")
    private LocalDateTime blurUpdateAt;

    /* 최종 blur 작업일시 */
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    @Column(name = "COMP_UPDATED_AT")
    private LocalDateTime compUpdateAt;

//    /* 시작일시 */
//    @NotNull
//    @Column(name = "START_AT", length = 30)
//    private String startAt;



//    /* 테이블 JOIN */
//    @OneToMany(fetch = FetchType.LAZY, mappedBy = "project") // 연관관계를 위한 컬렉션은 반드시 필드에서 초기화 하는 것이 안전함
//    private List<JobItem> jobItem = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "project")
    private List<ProjectFile> projectFile = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "project")
    private List<ProjectClass> projectClass = new ArrayList<>();

    @Builder
    public Project(String userEmail,Long projectId, String projectName, String projectCmt, String projectStatus, String blurYn, String delYn, String endAt, String endBy, List<ProjectFile> projectFile, List<ProjectClass> projectClass) {
        this.userEmail=userEmail;
        this.projectId = projectId;
        this.projectName = projectName;
        this.projectCmt = projectCmt;
        this.projectStatus = projectStatus;
        this.blurYn = blurYn;
        this.delYn = delYn;
        this.endAt = endAt;
        this.endBy = endBy;
        this.projectFile = projectFile;
        this.projectClass = projectClass;
    }

    public void setProject(Long projectId, String projectName, String projectCmt, String projectStatus, String blurYn, String delYn, String endAt, String endBy, String userEmail) {
        this.projectId = projectId;
        this.projectName = projectName;
        this.projectCmt = projectCmt;
        this.projectStatus = projectStatus;
        this.blurYn = blurYn;
        this.delYn = delYn;
        this.endAt = endAt;
        this.endBy = endBy;
        this.userEmail = userEmail;
    }

    public void setChangeProject(Long projectId, String projectName, String projectCmt, String blurYn, String endAt, String endBy) {
        this.projectId = projectId;
        this.projectName = projectName;
        this.projectCmt = projectCmt;
        this.blurYn = blurYn;
        this.endAt = endAt;
        this.endBy = endBy;
    }

    public void setBlurUpdateAt(LocalDateTime blurUpdateAt){
        this.blurUpdateAt = blurUpdateAt;
    }

    public void setLabelUpdateAt(LocalDateTime labelUpdateAt){
        this.labelUpdateAt = labelUpdateAt;
    }

    public void setCompUpdateAt(LocalDateTime compUpdateAt){
        this.compUpdateAt = compUpdateAt;
    }

}
