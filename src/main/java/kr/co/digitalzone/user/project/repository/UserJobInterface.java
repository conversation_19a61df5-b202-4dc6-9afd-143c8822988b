package kr.co.digitalzone.user.project.repository;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

public interface UserJobInterface {
    Long getJobId();

    String getJobName();

    @JsonFormat(pattern = "yyyy-MM-dd")
    LocalDateTime getRegisteredAt();

    String getEndAt();

    Long getFileCount();

    Long getBlurCount();

    Long getLabelCount();

    Double getTotalCount();

    String getJobStatus();
}
