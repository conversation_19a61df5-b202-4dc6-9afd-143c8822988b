package kr.co.digitalzone.user.project.request;

import lombok.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class UserProjectReqDto {
    // 프로젝트 생성
    @Getter @Setter
    public static class ProjectSaveRequest {
        private Long projectId;
        //        private String ccode;
        private String projectName;
        private String projectCmt;
        private String projectStatus;
        private String blurYn;
//        private String autoLabelYn;
        private String delYn;
        private String endAt;
        private String endBy;
        private String email;
//        private String startAt;
    }

}
