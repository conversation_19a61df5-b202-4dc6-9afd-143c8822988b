package kr.co.digitalzone.user.project.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.querydsl.core.annotations.QueryProjection;
import jakarta.persistence.Convert;
import kr.co.digitalzone.user.common.dto.ResponsePageDto;
import kr.co.digitalzone.user.project.domain.Project;
import lombok.*;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class UserProjectRespDto {
    // 프로젝트 생성
    @Getter
    @Setter
    public static class ProjectSaveResponse {
        private Long projectId;
        private String projectName;
        private String projectCmt;
        private String projectStatus;
        private String blurYn;
        private String delYn;
        private String endAt;
        private String endBy;

        public ProjectSaveResponse(Project project) {
            this.projectId = project.getProjectId();
//            this.ccode = project.getCcode();
            this.projectName = project.getProjectName();
            this.projectCmt = project.getProjectCmt();
            this.projectStatus = project.getProjectStatus();
            this.blurYn = project.getBlurYn();
            this.delYn = project.getDelYn();
            this.endAt = project.getEndAt();
            this.endBy = project.getEndBy();
//            this.startAt = project.getStartAt();
        }
    }
    // 프로젝트 내역 조회
    @Getter @Setter
    public static class UserProjectResponse {
        private Long projectId;
        private String projectName;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime registeredAt;
        private String endAt;
        private Long fileCount;
        private Long blurCount;
        private Long labelCount;
        private String progress;
        private String projectStatus;
        private String blurYn;
        @QueryProjection
        public UserProjectResponse(Long projectId, String projectName, LocalDateTime registeredAt, String endAt, String projectStatus,String blurYn) {
            this.projectId = projectId;
            this.projectName = projectName;
            this.registeredAt = registeredAt;
            this.endAt = endAt;
            this.projectStatus = projectStatus;
            this.blurYn=blurYn;
        }
    }
    // 비식별화,라벨수
    @Getter @Setter
    public static class UserProjectblurResponse {
        private Integer blurCount;
        private Integer labelCount;

        @QueryProjection
        public UserProjectblurResponse(Integer blurCount, Integer labelCount) {
            this.blurCount = blurCount;
            this.labelCount = labelCount;
        }
    }

    // 진행률
    @Getter @Setter
    public static class UserProgressResponse {
        private Long fileTotalCount;
        private Long fileCount;

        @QueryProjection
        public UserProgressResponse(Long fileTotalCount, Long fileCount) {
            this.fileTotalCount = fileTotalCount;
            this.fileCount = fileCount;
        }
    }


    // 프로젝트/작업목록 조회
    @Getter @Setter
    public static class UserJobResponse {
        private Long projectId;
        private String projectName;
        private String projectCmt;
        private String endAt;
        private String blurYn;
        private String projectStatus;
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Seoul")
        private LocalDateTime registeredAt;
        @QueryProjection
        public UserJobResponse(Long projectId, String projectName, String projectCmt, String endAt, LocalDateTime registeredAt, String blurYn, String projectStatus) {
            this.projectId = projectId;
            this.projectName = projectName;
            this.projectCmt = projectCmt;
            this.endAt = endAt;
            this.registeredAt = registeredAt;
            this.blurYn = blurYn;
            this.projectStatus = projectStatus;
        }
    }

    // 프로젝트 작업 현황 정보
    @Getter @Setter
    public static class ProjectProgressResponse{
        private Integer blurCnt;
        private Integer labelCnt;
//        private Integer labelDoneCnt;
        private Integer compCnt;
        private String blurPer;
        private String labelPer;
        private String compPer;
        private Long totalCnt;
        @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        private LocalDateTime blurLastAt;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        private LocalDateTime labelLastAt;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        private LocalDateTime compLastAt;
        @QueryProjection
        public ProjectProgressResponse(Integer blurCnt, Integer labelCnt, Integer compCnt, Long totalCnt, LocalDateTime blurLastAt,LocalDateTime labelLastAt,LocalDateTime compLastAt) {
            this.blurCnt = blurCnt;
            this.labelCnt = labelCnt;
//            this.labelDoneCnt = labelDoneCnt;
            this.compCnt = compCnt;
            this.totalCnt = totalCnt;
            this.blurLastAt = blurLastAt;
            this.labelLastAt = labelLastAt;
            this.compLastAt = compLastAt;
        }

    }

    // 작업리스트
    @Getter @Setter
    public static class JobItemResponse {
        private Long projectId;
        private String projectName;
//        private Long jobId;
//        private String jobName;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime registeredAt;
        private String endAt;
        private Long fileCount;
        private Long blurCount;
        private Long labelCount;
        private String progress;
//        private String jobStatus;
        @QueryProjection
        public JobItemResponse(Long projectId, String projectName, LocalDateTime registeredAt, String endAt, Long fileCount) {
            this.projectId = projectId;
            this.projectName = projectName;
//            this.jobId = jobId;
//            this.jobName = jobName;
            this.registeredAt = registeredAt;
            this.endAt = endAt;
            this.fileCount = fileCount;
//            this.jobStatus = jobStatus;
        }
    }

    // 작업 비식별화,라벨링 수
    @Getter @Setter
    public static class JobBlurResponse {
        private Integer blurCount;
        private Integer labelCount;

        @QueryProjection
        public JobBlurResponse(Integer blurCount, Integer labelCount) {
            this.blurCount = blurCount;
            this.labelCount = labelCount;
        }
    }

    // 작업 진행률
    @Getter @Setter
    public static class JobProgressResponse {
        private Long fileTotalCount;
        private Long fileCount;

        @QueryProjection
        public JobProgressResponse(Long fileTotalCount, Long fileCount) {
            this.fileTotalCount = fileTotalCount;
            this.fileCount = fileCount;
        }
    }
}
