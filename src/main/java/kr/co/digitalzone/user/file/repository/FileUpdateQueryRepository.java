package kr.co.digitalzone.user.file.repository;

import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.persistence.EntityManager;
import kr.co.digitalzone.user.project.domain.Project;
import lombok.RequiredArgsConstructor;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;

@Repository
@RequiredArgsConstructor
public class FileUpdateQueryRepository {
    private final EntityManager em;

    public long updateFile(@Param("fileId") List<Long> fileIdList, Project project) {
        JPAQueryFactory query = new JPAQueryFactory(em);
        return query.update(projectFile)
                .set(projectFile.project.projectId, project.getProjectId())
                .where(projectFile.fileId.in(fileIdList))
                .execute();
    }
}