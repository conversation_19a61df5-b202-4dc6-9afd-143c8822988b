package kr.co.digitalzone.user.file.repository;

import kr.co.digitalzone.user.file.domain.ProjectFile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FileRepository extends JpaRepository<ProjectFile, Long> {

    /* 파일리스트 조회 */
    @Query("select f from AIL_PROJECT_FILE f where f.delYn = 'N' ")
    List<ProjectFile> findAll();

    /* 파일삭제 */
    @Modifying
    @Query("delete from AIL_PROJECT_FILE f where f.project.projectId = :projectId ")
    void deleteByProjectFile(@Param("projectId") Long projectId);

    /* 파일 반환데이터 조회 */
    @Query("select f from AIL_PROJECT_FILE f where f.fileId = :fileId ")
    ProjectFile findAllById(@Param("fileId") Long fileId);

    /* 파일 존재 여부 확인 */
    boolean existsById(Long fileId);

    boolean existsByFileName(String fileName);

}
