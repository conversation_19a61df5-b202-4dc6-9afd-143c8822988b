package kr.co.digitalzone.user.file.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import kr.co.digitalzone.user.jobItem.domain.JobLabel;
import kr.co.digitalzone.user.common.domain.CommonEntity;
import kr.co.digitalzone.user.project.domain.Project;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@SequenceGenerator(
        name = "FILE_ID",
        sequenceName = "seq_file_id",
        allocationSize = 1, initialValue = 1
)
@Entity(name = "AIL_PROJECT_FILE")
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class ProjectFile extends CommonEntity {
    /* 파일아이디 */
    @Id
    @GeneratedValue(
            strategy=GenerationType.SEQUENCE,
            generator="FILE_ID"
    )
    @Column(name = "FILE_ID")
    private Long fileId;

    /* 회사코드 */
//    @NotNull
//    @Column(name = "CCODE", length = 36)
//    private String ccode;

    /* 프로젝트번호 FK */
    @ManyToOne(fetch = FetchType.LAZY)
//    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE) //
    @JoinColumn(name = "PROJECT_ID")
    private Project project;

//    /* 작업번호 FK */
//    @NotFound(action = NotFoundAction.IGNORE)
//    @ManyToOne(fetch = FetchType.LAZY) // cascade = CascadeType.ALL -> 영속성 전이
////    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
//    @JoinColumn(name = "JOB_ID")
//    private JobItem jobItem;

    /* 실제파일명 */
    @Column(name = "FILE_NAME")
    private String fileName;

    /* 저장파일명 */
    @Column(name = "FILE_REAL")
    private String fileReal;

    /* 파일경로명 */
    @Column(name = "FILE_URL")
    private String fileUrl;

    /* 파일상태 */
    @Column(name = "FILE_STATUS", length = 10)
    private String fileStatus;

    /* 라벨링유무 */
    @Column(name = "FILE_LABELING", length = 10)
    private String fileLabeling;

    /* 비식별화유무 */
    @Column(name = "FILE_BLURING", length = 10)
    private String fileBluring;

    /* 오토라벨링유무 */
//    @Column(name = "FILE_AUTOLABELING", length = 10)
//    private String fileAutolabeling;

    /* 파일확장자 */
    @NotNull
    @Column(name = "FILE_EXT", length = 10)
    private String fileExt;

    /* 파일유형 */
    @NotNull
    @Column(name = "FILE_TYPE", length = 10)
    private String fileType;

    /* 파일크기 */
    @NotNull
    @Column(name = "SIZE")
    private Long size;

    /* 가로값 */
    @Column(name = "WIDTH")
    private Long width;

    /* 세로값 */
    @Column(name = "HEIGHT")
    private Long height;

    /* 삭제여부 */
    @NotNull
    @Column(name = "DEL_YN", length = 10)
    private String delYn;

    /* Join 테이블 */
//    @OneToMany(fetch = FetchType.LAZY, mappedBy = "projectFile")
//    private List<JobFile> jobFile = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "projectFile")
    private List<JobLabel> jobLabel = new ArrayList<>();

    @Builder
    public ProjectFile(Long fileId, Project project, String fileName, String fileReal, String fileUrl, String fileStatus, String fileLabeling, String fileBluring, String fileExt, String fileType, Long size, Long width, Long height, String delYn) {
        this.fileId = fileId;
        this.project = project;
        this.fileName = fileName;
        this.fileReal = fileReal;
        this.fileUrl = fileUrl;
        this.fileStatus = fileStatus;
        this.fileLabeling = fileLabeling;
        this.fileBluring = fileBluring;
        this.fileExt = fileExt;
        this.fileType = fileType;
        this.size = size;
        this.width = width;
        this.height = height;
        this.delYn = delYn;
    }

    public void blurSave(String fileReal, String fileUrl){
        this.fileReal = fileReal;
        this.fileUrl = fileUrl;
    }

    public void blurComp(String fileReal, String fileUrl, String fileBluring){
        this.fileReal = fileReal;
        this.fileUrl = fileUrl;
        this.fileBluring = fileBluring;
    }

    public void labelSave(String fileLabeling){
        this.fileLabeling = fileLabeling;
    }
}
