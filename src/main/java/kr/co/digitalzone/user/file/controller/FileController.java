//package kr.co.digitalzone.user.file.controller;
//
//import kr.co.digitalzone.user.common.dto.ResponseDto;
////import kr.co.digitalzone.user.file.service.FileService;
//import kr.co.digitalzone.user.file.request.FileReqDto;
//import kr.co.digitalzone.user.project.domain.Project;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.util.List;
//import java.util.Map;
//
//
//@Validated
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/user")
//@Slf4j
//public class FileController {
//    private final FileService fileService;
//
//    /* 파일업로드 */
//    @PostMapping("/uploadFile")
//    public ResponseEntity<?> uploadFile(@RequestPart(value = "files") MultipartFile[] files, Project project) {
//        fileService.uploadFile(files, project);
//
//        return new ResponseEntity<>(new ResponseDto<>(200, "파일업로드 성공", null), HttpStatus.CREATED);
//    }
//
//    /* 파일유형 불러오기 */
//    @PostMapping("/findFile")
//    public ResponseEntity<?> findFile(@RequestBody FileReqDto.FileSaveRequest request) {
////        List<FileListResponse> findFile = fileService.findAll(request);
//
//        return new ResponseEntity<>(new ResponseDto<>(200, "파일찾기 성공", null), HttpStatus.OK);
//    }
//
//    /* 프로젝트번호 변경 -> 파일수정 */
//    @PostMapping("/updateFile")
//    public ResponseEntity<?> updateFile(@RequestPart List<Map<String, Object>> fileUpdateResponse, Project project) {
//        fileService.updateFile(fileUpdateResponse, project);
//
//        return new ResponseEntity<>(new ResponseDto<>(200, "파일찾기 성공", null), HttpStatus.OK);
//    }
//}
