package kr.co.digitalzone.user.file.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

@Slf4j
public class FileUtils extends org.apache.commons.io.FileUtils {

	/**
	 * @param checkExt (유효한 파일 확장자명)
	 * @param multipartFile (체크할 파일)
	 * @return 성공시 true, 실패시 false
	 */
	public static boolean checkFileExtension(String[] checkExt, MultipartFile multipartFile) {

		boolean flag = false;

		if (multipartFile != null && !multipartFile.isEmpty()) {
			String fileName = multipartFile.getOriginalFilename();
			String fileExt = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
			for (int i = 0; i < checkExt.length; i++) {
				if (checkExt[i].toUpperCase().equals(fileExt.toUpperCase())) {
					flag = true;
					break;
				}
			}
		}
		return flag;
	}
	/* 파일 생성 */
	public static void createFileWithDirectories(String filePath) throws IOException {
		File file = new File(filePath);

		if (!file.exists()) {
			if (!file.createNewFile()) {
				throw new IOException("Failed to create file: " + file.getAbsolutePath());
			}
		}
	}
}
