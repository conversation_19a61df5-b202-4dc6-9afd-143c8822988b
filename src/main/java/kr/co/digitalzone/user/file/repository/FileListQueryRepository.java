//package kr.co.digitalzone.user.file.repository;
//
//import com.querydsl.jpa.impl.JPAQueryFactory;
//import jakarta.persistence.EntityManager;
//import kr.co.digitalzone.user.file.response.QFileRespDto_FileListResponse;
//import lombok.RequiredArgsConstructor;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//import static kr.co.digitalzone.user.file.domain.QProjectFile.projectFile;
//import static kr.co.digitalzone.user.file.request.FileReqDto.FileSaveRequest;
//import static kr.co.digitalzone.user.file.response.FileRespDto.FileListResponse;
//
//@Repository
//@RequiredArgsConstructor
//public class FileListQueryRepository {
//    private final EntityManager em;
//
//    public List<FileListResponse> findAll(FileSaveRequest request) {
//        JPAQueryFactory query = new JPAQueryFactory(em);
//        return query.select(
//                new QFileRespDto_FileListResponse(
//                          projectFile.fileId
//                        , projectFile.fileName
//                        , projectFile.fileReal
//                        , projectFile.fileExt
//                        , projectFile.fileStatus
//                        , projectFile.fileLabeling
//                        , projectFile.fileBluring
//                        , projectFile.fileAutolabeling
//                        , projectFile.fileType
//                        , projectFile.size
//                        , projectFile.delYn
//                        )
//                )
//                .from(projectFile)
//                .where(projectFile.delYn.eq("N")
//                        , projectFile.ccode.eq(request.getCcode()))
//                .orderBy(projectFile.fileId.desc())
//                .fetch();
//    }
//}