package kr.co.digitalzone.user.file.request;

import lombok.*;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class FileReqDto {

    // 파일 업로드
    @Getter @Setter
    public static class FileSaveRequest {
        private Long fileId;
        private String ccode;
        private Long projectId;
        private String fileName;
        private String fileReal;
        private String fileUrl;
        private String fileStatus;
        private String fileLabeling;
        private String fileBluring;
//        private String fileAutolabeling;
        private String fileExt;
        private String fileType;
        private String size;
        private String delYn;
    }

    // 작업파일 변경
    @Getter @Setter
    public static class JobFileUpdateRequest {
        private Long fileId;
        private Long jobId;
        private Long fileKey;
    }

}
