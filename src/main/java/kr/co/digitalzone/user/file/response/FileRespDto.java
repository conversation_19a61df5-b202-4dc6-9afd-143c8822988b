package kr.co.digitalzone.user.file.response;

import com.querydsl.core.annotations.QueryProjection;
import kr.co.digitalzone.user.file.domain.ProjectFile;
//import kr.co.digitalzone.svAdmin.jobItem.domain.JobFile;
//import kr.co.digitalzone.svAdmin.jobItem.domain.JobItem;
import lombok.*;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class FileRespDto {

    // 파일 저장
    @Getter @Setter
    public static class FileSaveResponse{
        private Long fileId;
//        private String ccode;
        private Long projectId;
        private String fileName;
        private String fileReal;
        private String fileUrl;
        private String fileStatus;
        private String fileLabeling;
        private String fileBluring;
//        private String fileAutolabeling;
        private String fileExt;
        private String fileType;
        private Long size;
        private String delYn;

        public FileSaveResponse(ProjectFile projectFile) {
            this.fileId = projectFile.getFileId();
//            this.ccode = projectFile.getCcode();
            this.projectId = projectFile.getProject().getProjectId();
//            this.jobId = projectFile.getJobItem().getJobId();
            this.fileName = projectFile.getFileName();
            this.fileReal = projectFile.getFileReal();
            this.fileUrl = projectFile.getFileUrl();
            this.fileStatus = projectFile.getFileStatus();
            this.fileLabeling = projectFile.getFileLabeling();
            this.fileBluring = projectFile.getFileBluring();
//            this.fileAutolabeling = projectFile.getFileAutolabeling();
            this.fileExt = projectFile.getFileExt();
            this.fileType = projectFile.getFileType();
            this.size = projectFile.getSize();
            this.delYn = projectFile.getDelYn();
        }
    }

    // 파일 조회
    @Getter @Setter
    public static class FileListResponse {
        private Long fileId;
        private String fileName;
        private String fileReal;
        private String fileExt;
        private String fileStatus;
        private String fileLabeling;
        private String fileBluring;
//        private String fileAutolabeling;
        private String fileType;
        private Long size;
        private String delYn;

        @QueryProjection
        public FileListResponse(Long fileId, String fileName, String fileReal, String fileExt, String fileStatus, String fileLabeling, String fileBluring, String fileType, Long size, String delYn) {
            this.fileId = fileId;
            this.fileName = fileName;
            this.fileReal = fileReal;
            this.fileExt = fileExt;
            this.fileStatus = fileStatus;
            this.fileLabeling = fileLabeling;
            this.fileBluring = fileBluring;
//            this.fileAutolabeling = fileAutolabeling;
            this.fileType = fileType;
            this.size = size;
            this.delYn = delYn;
        }
    }

    // 파일 수정
    @Getter @Setter
    public static class FileUpdateResponse {
        private Long fileId;
        private Long projectId;

        @QueryProjection
        public FileUpdateResponse(Long fileId, Long projectId) {
            this.fileId = fileId;
            this.projectId = projectId;
        }
    }

//    // 작업파일 변경
//    @Getter @Setter
//    public static class JobFileUpdateResponse {
//        private Long fileId;
//        private List<JobFile> fileKey;
//        private List<JobItem> jobItems = new ArrayList<>();
//
//        public JobFileUpdateResponse(Long fileId, List<JobFile> fileKey) {
//            this.fileId = fileId;
//            this.fileKey = fileKey;
//        }
//    }

    // json 파일
    @Getter @Setter
    public static class JsonFileListResponse {
        private Long classId;
        private String className;
        private String classColor;
        private String classType;

        @QueryProjection
        public JsonFileListResponse(Long classId, String className, String classColor, String classType) {
            this.classId = classId;
            this.className = className;
            this.classColor = classColor;
            this.classType = classType;
        }
    }
}
