package kr.co.digitalzone.user.file.service;

import kr.co.digitalzone.user.file.domain.ProjectFile;
import kr.co.digitalzone.user.file.domain.QProjectFile;
import kr.co.digitalzone.user.file.repository.FileRepository;
import kr.co.digitalzone.user.file.repository.FileUpdateQueryRepository;
import kr.co.digitalzone.user.file.response.FileRespDto;
import kr.co.digitalzone.user.file.util.FileUtils;
import kr.co.digitalzone.user.project.domain.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = false)
@Slf4j
public class UserFileService {
    private final FileRepository fileRepository;
    //    private final FileListQueryRepository fileListQueryRepository;
    private final FileUpdateQueryRepository fileUpdateQueryRepository;

    private final ExecutorService executorService;
    private final DataSourcePoolMetadataProvidersConfiguration dataSourcePoolMetadataProvidersConfiguration;
//    private final JobFileRepository jobFileRepository;

    //파일경로
    @Value("${spring.file.upload.path}")
    String filePath;

    @Value("${spring.file.thumbnail.path}")
    String thumbnailPath;

    @Value("${spring.file.worked.path}")
    String workedPath;

    /* 파일 업로드 */
    @Transactional
    public void uploadFile(MultipartFile[] files, Project project) {
        File folder = new File(filePath);

        // 썸네일 생성경로
        String thumUrl = thumbnailPath;
        File tFolder = new File(thumUrl);

        if (!tFolder.exists()) {
            tFolder.mkdirs();
        }

        String uploadLastPath = filePath;
        String uploadPath = "";
        String savePath = "";

        if (files != null) {
            for (MultipartFile file : files) {
                // bytes 배열의 크기를 1024로 지정
                byte[] bytes = new byte[1024];

                try {
                    // 파일 확장자
                    String extention = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
                    // 파일명 랜덤생성
//                    String realName = String.valueOf(UUID.randomUUID());
                    // 파일명 + 확장자
//                    String saveFileName = realName + "." + extention;
                    int idx = file.getOriginalFilename().lastIndexOf(".");
                    String fileNameEncode = file.getOriginalFilename().substring(0, idx);
                    String fileName = URLDecoder.decode(fileNameEncode, StandardCharsets.UTF_8.toString());
                    System.out.println("fileName : " + fileName);
                    // utf-8 적용 - 한글깨짐방지
//                    fileName=new String(fileName.getBytes("8859_1"),"UTF-8");
//                    String fileName = new String(file.getOriginalFilename().getBytes("8859_1") ,StandardCharsets.UTF_8).substring(0, idx);
//                    String fileName = new String(file.getOriginalFilename().getBytes("ISO-8859-1"), "UTF-8").substring(0, idx);

                    // C:/upload/
                    uploadPath = uploadLastPath;
                    savePath = File.separator.replaceAll("\\\\", "/") + fileName;

                    ProjectFile projectFile = ProjectFile
                            .builder()
                            .project(project)
//                            .jobItem(project.getJobItem().get(0))
                            .fileName(fileName)
                            .fileReal(fileName)
                            .fileUrl(savePath + "." + extention)
                            .fileStatus("P")    /// 처음부터 작업중 상태로 생성
                            .fileLabeling("N")
                            .fileBluring("N".equals(project.getBlurYn()) ? "Y" : "N")
                            .fileExt(extention)
                            .fileType("I")
                            .size(file.getSize())
                            .delYn("N")
                            .build();
                    ProjectFile save = fileRepository.save(projectFile);
                    new FileRespDto.FileSaveResponse(save);

                    Path copyOfLocation = Paths
                            .get(filePath + File.separator + StringUtils.cleanPath(fileName + "." + extention));
                    log.info("fileNameEncode" + fileNameEncode);

                    Files.copy(file.getInputStream(), copyOfLocation, StandardCopyOption.REPLACE_EXISTING);

                    // 파일 생성
//                    String createFile = uploadPath + saveFileName;
//                    file.transferTo(new File(createFile));

                    Runnable runnable = new Runnable() {
                        @Override
                        public void run() {
                            // 썸네일 파일명
                            File tFile = new File(tFolder + File.separator + fileName + "." + extention);
                            // 썸네일
                            BufferedImage oImage = null;
                            try {
                                oImage = ImageIO.read(file.getInputStream());
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                            int tWidth = 50; // 썸네일이미지 너비
                            int tHeight = 50; // 썸네일이미지 높이

                            // 썸네일이미지
                            BufferedImage tImage = new BufferedImage(tWidth, tHeight, BufferedImage.TYPE_3BYTE_BGR);
                            Graphics2D graphic = tImage.createGraphics();
                            Image image = oImage.getScaledInstance(tWidth, tHeight, Image.SCALE_SMOOTH);
                            graphic.drawImage(image, 0, 0, tWidth, tHeight, null);
                            graphic.dispose(); // 리소스를 모두 해제

                            try {
                                ImageIO.write(tImage, extention, tFile);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    };

                    executorService.submit(runnable);

                } catch (IOException e) {
                    e.printStackTrace();
                    throw new IllegalStateException("파일 업로드 중 오류가 발생하였습니다");
                }
            }
        }
    }

    public boolean existsById(Long fileId) {
        return fileRepository.existsById(fileId);
    }

   public void uploadBlurImg(MultipartFile file, ProjectFile projectFile){
        if(!file.isEmpty()){
            try {
                String orifilePath = projectFile.getFileUrl();
                String blurImgPath = workedPath+orifilePath;
                if (projectFile.getFileBluring().equals("N")){
                    blurImgPath = workedPath + "/" + projectFile.getFileReal() + ".dat." + projectFile.getFileExt();
                }
                File blurImg = new File(blurImgPath);
                FileUtils.copyInputStreamToFile(file.getInputStream(), blurImg);
            } catch (IOException e) {
                throw new IllegalStateException("파일 업로드 중 오류가 발생하였습니다");
            }
        }
    }
}