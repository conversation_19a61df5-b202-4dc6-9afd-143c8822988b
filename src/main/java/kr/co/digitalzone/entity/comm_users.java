
package kr.co.digitalzone.entity;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.format.annotation.DateTimeFormat;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity(name="comm_users")
@DynamicUpdate // 변경할 필드만 대응
@DynamicInsert // 지정한 값만 insert
public class comm_users implements Serializable {
  @Id
  private String id;
  private String email;
  private String verified;
  private String name;
  private String phone;
  @Column(name = "PASSWORD",length = 300)
  private String password;
  // @JsonIgnore
//  private String password_1;
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date registeredAt;
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private Date updatedAt;
  private String registerIp;
  private String updaterIp;
  private String registeredBy;
  private String updatedBy;
  private String status;
}
