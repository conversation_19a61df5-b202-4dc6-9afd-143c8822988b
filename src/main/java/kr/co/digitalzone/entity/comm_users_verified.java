package kr.co.digitalzone.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity(name="comm_users_verified")
@DynamicUpdate
@DynamicInsert
public class comm_users_verified {

  @Id
  private String verifiedKey;
  private String userId;
  private String status;
}
