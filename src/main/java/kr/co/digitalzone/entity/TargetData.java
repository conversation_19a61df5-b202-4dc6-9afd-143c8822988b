package kr.co.digitalzone.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "target_data")
@DynamicUpdate
@DynamicInsert
public class TargetData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "keyword")
    private String keyword;

    @Column(name = "target_count")
    private Integer targetCount;

    @Column(name = "source")
    private String source;

    @Column(name = "start_date")
    // @Temporal(TemporalType.TIMESTAMP)
    private Date startDate;

    @Column(name = "end_date")
    // @Temporal(TemporalType.TIMESTAMP)
    private Date endDate;

    @Column(name = "processing_count")
    private String processingCount;

    @Column(name = "collected_count")
    private Integer collectedCount;

    @Column(name = "stats")
    private String stats;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "job_id", unique = true) // 추가: 수집 작업 고유 ID
    private String jobId;

}