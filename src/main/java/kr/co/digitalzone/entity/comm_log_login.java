//package kr.co.digitalzone.entity;
//
//import jakarta.persistence.Column;
//import jakarta.persistence.Entity;
//import jakarta.persistence.GeneratedValue;
//import jakarta.persistence.GenerationType;
//import jakarta.persistence.Id;
//import jakarta.persistence.SequenceGenerator;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//import org.hibernate.annotations.DynamicInsert;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@Entity(name = "comm_log_login")
//@DynamicInsert
//@SequenceGenerator(name = "mem_logrecord_id_seq", sequenceName = "mem_logrecord_id_seq", allocationSize = 1)
//public class comm_log_login {
//  @Id
//  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "mem_logrecord_id_seq")
//  private int id;
//  @Column(name = "memberId")
//  private String userId;
//  private String status;
//  private Date registeredAt;
//}
