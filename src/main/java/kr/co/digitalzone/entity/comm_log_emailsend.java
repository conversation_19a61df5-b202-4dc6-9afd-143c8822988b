//package kr.co.digitalzone.entity;
//
//import jakarta.persistence.Entity;
//import jakarta.persistence.GeneratedValue;
//import jakarta.persistence.GenerationType;
//import jakarta.persistence.Id;
//import jakarta.persistence.SequenceGenerator;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.annotations.DynamicInsert;
//import org.hibernate.annotations.DynamicUpdate;
//import org.springframework.format.annotation.DateTimeFormat;
//
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//@Entity(name="comm_log_emailsend")
//@SequenceGenerator(name = "email_send_log_seq", sequenceName = "email_send_log_seq", allocationSize = 1)
//@DynamicUpdate // 변경할 필드만 대응
//@DynamicInsert // 지정한 값만 insert
//public class comm_log_emailsend {
//  @Id
//  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "email_send_log_seq")
//  private int id;
//  private String status;
//  private String typeCd;
//  private int templateId;
//  private String emailFrom;
//  private String emailTo;
//  private String subject;
//  private String content;
//  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//  private Date sentAt;
//  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//  private Date registeredAt;
//  private Date updatedAt;
//  private String registeredIp;
//  private String updatedIp;
//  private String registeredBy;
//  private String updatedBy;
//}