//package kr.co.digitalzone.entity;
//
//import jakarta.persistence.Entity;
//import jakarta.persistence.Id;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.Getter;
//import lombok.NoArgsConstructor;
//import lombok.Setter;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
//@Entity(name = "comm_email_template")
//public class comm_email_template {
//  @Id
//  private String id;
//  private String title;
//  private String content;
//  private Date registeredAt;
//  private Date updatedAt;
//  private String registeredBy;
//  private String updatedBy;
//}
