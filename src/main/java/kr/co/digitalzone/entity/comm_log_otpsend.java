//package kr.co.digitalzone.entity;
//
//import jakarta.persistence.Column;
//import jakarta.persistence.Entity;
//import jakarta.persistence.GeneratedValue;
//import jakarta.persistence.GenerationType;
//import jakarta.persistence.Id;
//import jakarta.persistence.SequenceGenerator;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.annotations.DynamicInsert;
//import org.hibernate.annotations.DynamicUpdate;
//import org.springframework.format.annotation.DateTimeFormat;
//
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//@Entity(name = "comm_log_otpsend")
//@DynamicUpdate // 변경할 필드만 대응
//@DynamicInsert // 지정한 값만 insert
//@SequenceGenerator(name = "otp_send_log_id_seq", sequenceName = "otp_send_log_id_seq", allocationSize = 1)
//public class comm_log_otpsend {
//
//  @Id
//  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "otp_send_log_id_seq")
//  private int id;
//  @Column(name = "memberId")
//  private String userId;
//  private String otpPin;
//  private String verified;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date registeredAt;
//  private String registerIp;
//  private String updaterIp;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date updatedAt;
//  private String registeredBy;
//  private String updatedBy;
//}
