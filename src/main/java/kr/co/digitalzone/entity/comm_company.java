//package kr.co.digitalzone.entity;
//
//import jakarta.persistence.Entity;
//import jakarta.persistence.Id;
//import java.util.Date;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//import org.hibernate.annotations.DynamicInsert;
//import org.hibernate.annotations.DynamicUpdate;
//import org.springframework.format.annotation.DateTimeFormat;
//
//@Data
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//// @SequenceGenerator(name = "company_id_seq", sequenceName = "company_id_seq", allocationSize = 1)
//@Entity(name="comm_company")
//@DynamicUpdate // 변경할 필드만 대응
//@DynamicInsert // 지정한 값만 insert
//public class comm_company {
//  @Id
//
//  private String companyId;
//  private String companyName;
//  private String companyPhone;
//  private String department;
//  private String managerName;
//  private String managerPhone;
//  private String managerEmail;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date registeredAt;
//  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
//  private Date updatedAt;
//  private String registerIp;
//  private String updaterIp;
//  private String registeredBy;
//  private String updatedBy;
//  private String status;
//}