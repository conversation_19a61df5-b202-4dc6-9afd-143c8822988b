package kr.co.digitalzone.crawling.images.controller;


import jakarta.validation.Valid;
import kr.co.digitalzone.crawling.images.service.ImageService;
//import kr.co.digitalzone.response.ApiResponse;
//import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.dto.ImageCollectionReqDto;
import kr.co.digitalzone.dto.ImageCollectionResultDto;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/crawling")
public class ImageController {

    @Autowired
    private ImageService imageService;

//    @PostMapping("/images")
//    public String collectImages(@RequestParam String keyword, @RequestParam int collect_count) {
//        return imageService.collectImages(keyword, collect_count);
//    }
//    @PostMapping("/images")
//    public ResponseEntity<String> collectImages(@RequestParam String keyword, @RequestParam int collect_count)  {
//        String result = imageService.collectImages(keyword, collect_count);
//        return ResponseEntity.ok(result);
//    }
//    @GetMapping("/progress")
//    public ResponseEntity<Integer> getProgress() {
//        int collectedCount = imageService.getCollectedImagesCount();
//        return ResponseEntity.ok(collectedCount);
//}
//    @GetMapping("/progress")
//    public Map<String, Object> getProgress() {
//        return imageService.getProgress();
//    }
//    @GetMapping("/detail")
//    public Map<String, Object> getDetail() {
//        return imageService.getDetail();
//    }

    /**
     * 이미지 수집 API
     * 요청: List<ImageCollectionRequestDto>
     * 응답: ResponseDto<List<ImageCollectionResultDto>>
     */
    @PostMapping("/images")
    public ResponseEntity<ResponseDto<List<ImageCollectionResultDto>>> collectImages(
            @Valid @RequestBody List<ImageCollectionReqDto> requests) {

        try {
            List<ImageCollectionResultDto> results = imageService.collectImages(requests);

            // 결과 분석
            long collectingCount = results.stream()
                    .filter(result -> "수집 중".equals(result.getStatus()))
                    .count();

            long failureCount = results.stream()
                    .filter(result -> "실패".equals(result.getStatus()) || "오류".equals(result.getStatus()))
                    .count();

            // 응답 메시지와 상태 코드 결정
            ResponseDto<List<ImageCollectionResultDto>> response;
            HttpStatus httpStatus;

            if (failureCount == 0 && collectingCount > 0) {
                // 모든 요청이 수집 중인 경우 (정상 시작)
                response = new ResponseDto<>(200, "이미지 수집 성공", results);
                httpStatus = HttpStatus.OK;
            } else if (collectingCount == 0) {
                // 모든 요청이 실패한 경우
                response = new ResponseDto<>(400, "이미지 수집 실패", results);
                httpStatus = HttpStatus.BAD_REQUEST;
            } else {
                // 일부 성공, 일부 실패한 경우
                String message = String.format("이미지 수집 부분 완료 (수집 중: %d, 실패: %d)",
                        collectingCount, failureCount);
                response = new ResponseDto<>(206, message, results);
                httpStatus = HttpStatus.PARTIAL_CONTENT;
            }

            return new ResponseEntity<>(response, httpStatus);

        } catch (Exception e) {
            ResponseDto<List<ImageCollectionResultDto>> errorResponse =
                    new ResponseDto<>(500, "이미지 수집 실패: " + e.getMessage(), null);

            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 데이터 수집 현황 조회
     *  대시보드
     *  @return
     */
    @GetMapping("/status")
    public Map<String, Object> getCrawlingStatus() {
        return imageService.getCrawlingStatus();
    }

    /**
     * 데이터 수집 카운트 현황 조회
     *  파이 그래프
     * @return
     */
    @GetMapping("/count")
    public Map<String, Object> getCrawlingCount() {
        return imageService.getCrawlingCount();
    }

    /**
     * 키워드별 데이터 보유 현황 조회
     *  막대그래프
     * @return
     */
    @GetMapping("/keywordCount")
    public Map<String, Object> getKeywordCount() {
        return imageService.getKeywordCount();
    }
}



