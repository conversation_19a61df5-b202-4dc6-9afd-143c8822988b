//package kr.co.digitalzone.crawling.images.service;
//
//import org.springframework.stereotype.Service;
//
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Service
//public class ImageService_local {
//    private int totalImages = 0;
//    private int collectedImages = 0;
//    private String currentKeyword = "";
//
//    public String collectImages(String keyword, int collect_count) {
//        this.totalImages = collect_count;
//        this.collectedImages = 0;
//        this.currentKeyword = keyword;
//}
//
//    public String collectImages(String keyword, int collect_count) {
////        로컬
//        String pythonScriptPath = "/Users/<USER>/rd/labelingService/labelingBackend/collect_images.py"; // Python 파일 경로
//        // 배포
////        String pythonScriptPath = "/volume1/pj1/backEnd/labeler/collect_images.py"; // Python 파일 경로 %%
//        String imageOutputDir = "/volume1/pj1/datalake/image"; // 이미지가 저장될 디렉토리 경로
//
//        List<String> command = new ArrayList<>();
//        command.add("bash");
//        command.add("-c");
//        // local
////        command.add("source /Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate && " +  // 가상 환경 활성화
////        command.add("source /volume1/pj1/backEnd/labeler/labeling/labeling/bin/activate && " +  // 가상 환경 활성화 %%
//        // 배포
////        command.add("source /volume1/pj1/backEnd/labeling/bin/activate && " +  // 가상 환경 활성화
//        command.add("source /Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate && " +  // 가상 환경 활성화
//                "python " + pythonScriptPath + " " + keyword + " " + collect_count);  // 파이썬 스크립트 실행
////                 "cd /volume1/pj1/backEnd/labeler&&"+
////
//
//
//        try {
//            ProcessBuilder processBuilder = new ProcessBuilder(command);
//            processBuilder.redirectErrorStream(true); // 오류 출력을 표준 출력으로 리디렉션
//            Process process = processBuilder.start();
//
//            // 프로세스의 출력을 읽어옴
//            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
//            StringBuilder output = new StringBuilder();
//            String line;
//            while ((line = reader.readLine()) != null) {
//                System.out.println(line); // 로그 출력
////                output.append(line).append("\n");
//            }
//
//            int exitCode = process.waitFor();
//            if (exitCode != 0) {
//                throw new RuntimeException("Python script execution failed with exit code: " + exitCode);
//            }
//
//            // 이미지 파일 목록을 읽어옴
//            List<String> imageFiles = Files.list(Paths.get(imageOutputDir))
//                    .map(path -> path.getFileName().toString())
//                    .collect(Collectors.toList());
//
//            return "이미지 수집이 완료되었습니다. 수집된 이미지 파일: " + String.join(", ", imageFiles);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new RuntimeException("Error executing Python script: " + e.getMessage());
//        }
//
//        public Map<String, Object> getProgress() {
//            Map<String, Object> progress = new HashMap<>();
//            progress.put("keyword", currentKeyword);
//            progress.put("total", totalImages);
//            progress.put("collected", collectedImages);
//            progress.put("progress", (double) collectedImages / totalImages * 100);
//            return progress;
//
//        }
//    }
//
//
////    public String collectImages(String keyword, int collectCount) {
////        try {
////            String[] command = new String[]{
////                    "bash", "-c",
////                    "source /Users/<USER>/rd/labelingService/labelingBackend/labeling/bin/activate && " +
////                            "python3 /Users/<USER>/rd/labelingService/labelingBackend/collect_images.py " + keyword + " " + collectCount};  // ##
////
////            ProcessBuilder processBuilder = new ProcessBuilder(command);
////            processBuilder.redirectErrorStream(true);
////            Process process = processBuilder.start();
////
////            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
////            StringBuilder output = new StringBuilder();
////            char[] buffer = new char[1024];
////            int numRead;
////            // 프로세스의 출력을 읽어서 output에 저장
////            while ((numRead = reader.read(buffer)) != -1) {
////                output.append(buffer, 0, numRead);
////            }
////
////            boolean finished = process.waitFor(60, java.util.concurrent.TimeUnit.SECONDS); // 60초 타임아웃 설정
////            if (finished && process.exitValue() == 0) {
////                return "이미지 수집이 완료되었습니다.\n" + output.toString();
////            } else {
////                return "이미지 수집 중 오류가 발생했습니다.\n" + output.toString();
////            }
////        } catch (Exception e) {
////            return "이미지 수집 중 예외가 발생했습니다: " + e.getMessage();
////        }
////    }
////}