package kr.co.digitalzone.crawling.images.repository;

import org.springframework.stereotype.Repository;

import java.util.HashMap;

@Repository
public class ImageRepository {
    private int totalImages = 0;
    private int collectedImages = 0;
    private String currentKeyword = "";

    public int getTotalImages() {
        return totalImages;
    }

    public void setTotalImages(int totalImages) {
        this.totalImages = totalImages;
    }

    public int getCollectedImages() {
        return collectedImages;
    }

    public HashMap<String, Object> getImageDetails(String IMAGE_OUTPUT_DIR) {
        return new HashMap<String, Object>() {{
            put("totalImages", totalImages);
            put("collectedImages", collectedImages);
            put("currentKeyword", currentKeyword);
        }};
    }

    public void setCollectedImages(int collectedImages) {
        this.collectedImages = collectedImages;
    }

    public String getCurrentKeyword() {
        return currentKeyword;
    }

    public void setCurrentKeyword(String currentKeyword) {
        this.currentKeyword = currentKeyword;
    }
}
