package kr.co.digitalzone.email.service;

import java.util.Base64;
//import kr.co.digitalzone.dto.EmailsendlogDto;
import kr.co.digitalzone.dto.MembersDto;
//import kr.co.digitalzone.email.repository.EmailsendlogRepository;
import kr.co.digitalzone.member.repository.MembersRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class EmailService {
//  @Value("${front.server-url}")
//    private String frontServerUrl;
    private String frontServerUrl = null;
//  private final EmailsendlogRepository emailsendlogRepository;



  // 이메일 전송
//  public void sendEmail(EmailsendlogDto sendInfo) {
//    emailsendlogRepository.save(sendInfo.toEntity());
//  }
//
//  // 회원 가입시 이메일 전송 (URL에 id값 전달)
//  public String joinMembers(String verifiedKey) {
//
//    //메일 내용
//    String contents = "";
//
//
//    contents += "<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\" style=\"margin: auto;border: 1px solid #ccc;\">";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding: 20px 0; text-align: center\"> </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff; text-align: center; padding-bottom: 20px;\">";
//    contents += "            <div class=\"h6\" style=\"font-weight: bold;\">[WDT] 사용자 등록 메일 인증 안내입니다.</div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding:10px 20px;\">";
//    contents += "            <div style=\"background-color: #f8f8f8; font-size: 12px;padding: 20px;line-height: 1.2rem\">";
//    contents += "                <div style=\"margin-bottom: 10px; text-align: center;\">";
//    contents += "                    <div style=\"font-weight: bold;\">";
//    contents += "                        안녕하세요.<br>";
//    contents += "                        저희 서비스를 이용해 주셔서 감사합니다.<br>";
//    contents += "                        아래 [링크]를 클릭하여 주시면 회원가입이 완료됩니다.<br>";
//    contents += "                        감사합니다.";
//    contents += "                    </div>";
//    contents += "                </div>";
//    contents += "            </div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff;\">";
//    contents += "            <table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\">";
//    contents += "                <td";
//    contents += "                    style=\"padding:10px 20px; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;\">";
//    contents += "                    <table style=\"width: 100%;border-color: #ddd;\" border=\"1\">";
//    contents += "                        <tr>";
//    contents += "                            <th style=\"width: 20%;height: 42px;padding: 5px 10px;background-color: #f2f2f8\">링크";
//    contents += "                            </th>";
//    contents += "                            <td style=\"padding: 5px;\"><a href=\"" + frontServerUrl + "/user/auth/success_regist?key="+ verifiedKey +"\">http://192.168.0.210:33000/user/auth/success_regist</a></td>";
//
//    contents += "                        </tr>";
//    contents += "                    </table>";
//    contents += "                </td>";
//    contents += "    </tr>";
//    contents += "</table>";
//    contents += "</td>";
//    contents += "</tr>";
//    contents += "</table>";
//
//    return contents;
//  }
//
//  // 초대 이메일
//  public String inviteAdmin() {
//
//    String contents = "";
//
//    //메일 내용
//    contents += "<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\" style=\"margin: auto;border: 1px solid #ccc;\">";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding: 20px 0; text-align: center\"> </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff; text-align: center; padding-bottom: 20px;\">";
//    contents += "            <div class=\"h6\" style=\"font-weight: bold;\">[WDT] 사용자 등록 메일 인증 안내입니다.</div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding:10px 20px;\">";
//    contents += "            <div style=\"background-color: #f8f8f8; font-size: 12px;padding: 20px;line-height: 1.2rem\">";
//    contents += "                <div style=\"margin-bottom: 10px; text-align: center;\">";
//    contents += "                    <div style=\"font-weight: bold;\">";
//    contents+= "			                  <div class=\"h6\">이메일 인증이 완료되었습니다.</div>";
//    contents += "			                  <div class=\"h6\">로그인을 시도해주세요.</div>";
//    contents += "                    </div>";
//    contents += "                </div>";
//    contents += "            </div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff;\">";
//    contents += "            <table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\">";
//    contents += "                <td";
//    contents += "                    style=\"padding:10px 20px; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;\">";
//    contents += "                    <table style=\"width: 100%;border-color: #ddd;\" border=\"1\">";
//    contents += "                        <tr>";
//    contents += "                            <th style=\"width: 20%;height: 42px;padding: 5px 10px;background-color: #f2f2f8\">링크";
//    contents += "                            </th>";
//    contents += "                            <td style=\"padding: 5px;\"><a href=\"http://192.168.0.210:33000/user\">http://192.168.0.210:33000/user</a></td>";
//    contents += "                        </tr>";
//    contents += "                    </table>";
//    contents += "                </td>";
//    contents += "    </tr>";
//    contents += "</table>";
//    contents += "</td>";
//    contents += "</tr>";
//    contents += "</table>";
//
//    return contents;
//  }
//
//  // 이메일 수정시 인증메일 발송
//  public String modifyEmail(MembersDto member) {
//
//    String adminId = member.getId();
//    String encodeId = Base64.getUrlEncoder().encodeToString(adminId.getBytes());
//
//    //메일 내용
//    String contents = "";
//
//    contents += "<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\" style=\"margin: auto;border: 1px solid #ccc;\">";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding: 20px 0; text-align: center\"> </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff; text-align: center; padding-bottom: 20px;\">";
//    contents += "            <div class=\"h6\" style=\"font-weight: bold;\">[WDT] 이메일 인증 안내입니다.</div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "";
//    contents += "    <tr>";
//    contents += "        <td style=\"padding:10px 20px;\">";
//    contents += "            <div style=\"background-color: #f8f8f8; font-size: 12px;padding: 20px;line-height: 1.2rem\">";
//    contents += "                <div style=\"margin-bottom: 10px; text-align: center;\">";
//    contents += "                    <div style=\"font-weight: bold;\">";
//    contents += "                        안녕하세요.<br>";
//    contents += "                        저희 서비스를 이용해 주셔서 감사합니다.<br>";
//    contents += "                        아래 [링크]를 클릭하여 주시면 이메일 인증이 완료됩니다.<br>";
//    contents += "                        감사합니다.";
//    contents += "                    </div>";
//    contents += "                </div>";
//    contents += "            </div>";
//    contents += "        </td>";
//    contents += "    </tr>";
//    contents += "    <tr>";
//    contents += "        <td style=\"background-color: #ffffff;\">";
//    contents += "            <table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\">";
//    contents += "                <td";
//    contents += "                    style=\"padding:10px 20px; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;\">";
//    contents += "                    <table style=\"width: 100%;border-color: #ddd;\" border=\"1\">";
//    contents += "                        <tr>";
//    contents += "                            <th style=\"width: 20%;height: 42px;padding: 5px 10px;background-color: #f2f2f8\">링크";
//    contents += "                            </th>";
//    contents += "                            <td style=\"padding: 5px;\"><a href=\"http://192.168.0.210:33000/user/auth/auth_email?enc="+ encodeId +"\">http://192.168.0.210:33000/user/auth/auth_email</a></td>";
//    contents += "                        </tr>";
//    contents += "                    </table>";
//    contents += "                </td>";
//    contents += "    </tr>";
//    contents += "</table>";
//    contents += "</td>";
//    contents += "</tr>";
//    contents += "</table>";
//
//    return contents;
//  }
//
//  // 비밀번호 찾기, 비밀번호 초기화
//  public String resetPassword(String tempPassword) {
//
//    // 메일 내용
//    String contents = "";
//
//    contents += "<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\" style=\"margin: auto;border: 1px solid #ccc;\">";
//    contents += "	<tr>";
//    contents += "		<td style=\"padding: 20px 0; text-align: center\">";
//    contents += "		</td>";
//    contents += "	</tr>";
//
//    contents += "	<tr>";
//    contents += "		<td style=\"background-color: #ffffff; text-align: center;\">";
//    contents += "			<div class=\"h6\">임시 비밀번호가 아래와 같이 발급되었습니다.</div>";
//    contents += "			<div class=\"h6\">로그인 후 비밀번호를 반드시 변경하세요.</div>";
//    contents += "		</td>";
//    contents += "	</tr>";
//
//    contents += "	<tr>";
//    contents += "		<td style=\"background-color: #ffffff;\">";
//    contents += "			<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\">";
//    contents += "				<tr>";
//    contents += "					<td style=\"padding:10px 20px; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;\">";
//    contents += "						<table style=\"width: 100%;border-color: #ddd;\" border=\"1\">";
//    contents += "							<tr>";
//    contents += "								<th style=\"width: 30%;height: 42px; padding: 5px 10px;background-color: #f2f2f8\">임시 비밀번호</th>";
//    contents += "								<td style=\"padding: 5px;\">" + tempPassword + "</td>";
//    contents += "							</tr>";
//    contents += "						</table>";
//    contents += "					</td>";
//    contents += "				</tr>";
//    contents += "				<tr>";
//    contents += "					<td style=\"padding:10px 20px;\">";
//    contents += "						<div style=\"width: 100%;background-color: #f8f8f8;font-size: 12px;padding: 20px;line-height: 1.2rem; box-sizing: border-box;\">";
//    contents += "							<div style=\"margin-bottom: 10px;\">";
//    contents += "								<div style=\"font-weight: bold;\">이메일과 링크를 공유하지 마십시오.</div>";
//    contents += "								본 메일과 링크를 공유할 경우, 승인하지 않은 제 3자가 내용을 확인하고 서명할 수 있습니다. 이때 발생하는 문제에 대해서 본 서비스는 책임지지 않습니다.";
//    contents += "							</div>";
//    contents += "						</div>";
//    contents += "					</td>";
//    contents += "				</tr>";
//
//    contents += "			</table>";
//    contents += "		</td>";
//    contents += "	</tr>";
//
//    contents += "</table>";
//
//    return contents;
//  }

  // OTP 번호 전송 이메일
  public String sendOtp(String otp) {

    //메일 내용
    String contents = "";

    contents += "<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\" style=\"margin: auto;border: 1px solid #ccc;\">";
    contents += "	<tr>";
    contents += "		<td style=\"padding: 20px 0; text-align: center\">";
    contents += "		</td>";
    contents += "	</tr>";

    contents += "	<tr>";
    contents += "		<td style=\"background-color: #ffffff; text-align: center;\">";
    contents += "			<div class=\"h6\">인증번호가 아래와 같이 발급되었습니다.</div>";

    contents += "		</td>";
    contents += "	</tr>";

    contents += "	<tr>";
    contents += "		<td style=\"background-color: #ffffff;\">";
    contents += "			<table role=\"presentation\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\" width=\"100%\">";
    contents += "				<tr>";
    contents += "					<td style=\"padding:10px 20px; font-family: sans-serif; font-size: 15px; line-height: 20px; color: #555555;\">";
    contents += "						<table style=\"width: 100%;border-color: #ddd;\" border=\"1\">";
    contents += "							<tr>";
    contents += "								<th style=\"width: 30%;height: 42px; padding: 5px 10px;background-color: #f2f2f8\">인증 비밀번호</th>";
    contents += "								<td style=\"padding: 5px;\">" + otp + "</td>";
    contents += "							</tr>";
    contents += "						</table>";
    contents += "					</td>";
    contents += "				</tr>";
    contents += "				<tr>";
    contents += "					<td style=\"padding:10px 20px;\">";
    contents += "						<div style=\"width: 100%;background-color: #f8f8f8;font-size: 12px;padding: 20px;line-height: 1.2rem; box-sizing: border-box;\">";
    contents += "							<div style=\"margin-bottom: 10px;\">";
    contents += "								<div style=\"font-weight: bold;\">이메일과 링크를 공유하지 마십시오.</div>";
    contents += "								본 메일과 링크를 공유할 경우, 승인하지 않은 제 3자가 내용을 확인하고 서명할 수 있습니다. 이때 발생하는 문제에 대해서 본 서비스는 책임지지 않습니다.";
    contents += "							</div>";
    contents += "						</div>";
    contents += "					</td>";
    contents += "				</tr>";

    contents += "			</table>";
    contents += "		</td>";
    contents += "	</tr>";

    contents += "</table>";

    return contents;
  }
}
