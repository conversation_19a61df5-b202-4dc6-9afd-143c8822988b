package kr.co.digitalzone.member.service;

import jakarta.mail.internet.MimeMessage;
import jakarta.transaction.Transactional;
//import kr.co.digitalzone.dto.EmailsendlogDto;
import kr.co.digitalzone.dto.MembersDto;
//import kr.co.digitalzone.dto.OtpsendlogDto;
import kr.co.digitalzone.dto.UserVerifiedDto;
import kr.co.digitalzone.email.service.EmailService;
//import kr.co.digitalzone.entity.comm_log_otpsend;
import kr.co.digitalzone.entity.comm_users;
import kr.co.digitalzone.entity.comm_users_verified;
import kr.co.digitalzone.member.repository.MembersRepository;
//import kr.co.digitalzone.member.repository.OtpsendlogRepository;
import kr.co.digitalzone.member.repository.UserVerifiedRepository;
import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.util.CheckUtils;
import kr.co.digitalzone.util.PasswordUtils;
import kr.co.digitalzone.util.RequestUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.Pbkdf2PasswordEncoder;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Matcher;

@Service
@Slf4j
@RequiredArgsConstructor
public class MembersService {
    private final MembersRepository membersRepository;
//    private final OtpsendlogRepository otpsendlogRepository;
    private final Pbkdf2PasswordEncoder pbkdf2PasswordEncoder;
    private final EmailService emailService;
    private final UserVerifiedRepository userVerifiedRepository;
    private final JavaMailSender javaMailSender;

  @Value("${keys.password.iteration-count}")
  private int keysPasswordIterationCount;

  @Value("${keys.password.salt-length}")
  private int keysPasswordSaltLength;

  @Value("${keys.password.secret}")
  private String keysPasswordSecret;

  //이메일 중복 검사 (+유효성 검사)
  public long duplicateEmail(MembersDto member) throws Exception {

    // 2) 이메일 유효성 검사
    Matcher checkEmail = CheckUtils.checkEmail(member.getEmail());
    if (!checkEmail.find()) {
      throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
    }

    // 3) 이메일 중복 여부 확인
    long result = dupleEmail(member.getEmail());
    if (result > 0) {
      throw new Exception(ApiResponseEnum.DUPLICATE_EMAIL.getMessage());
    }
    return result;
  }

  // 회원가입
  public MembersDto joinMembers(MembersDto member) throws Exception {

    // 인증번호 결과 체크
    comm_users_verified verified = userVerifiedRepository.findByEmail(member.getEmail());
    if(verified==null || !"Y".equals(verified.getStatus())){
      throw new Exception(ApiResponseEnum.UNVERIFIED_EMAIL.getMessage());
    }

    // 3) 이메일 유효성 체크
    Matcher checkEmail = CheckUtils.checkEmail(member.getEmail());
    if (!checkEmail.find()) {
      throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
    }

    // 4) 비밀번호 암호화
    Pbkdf2PasswordEncoder passwordEncoder = new Pbkdf2PasswordEncoder(keysPasswordSecret, keysPasswordSaltLength, keysPasswordIterationCount, Pbkdf2PasswordEncoder.SecretKeyFactoryAlgorithm.PBKDF2WithHmacSHA512);
    String securePwd = passwordEncoder.encode(member.getPassword());
    member.setPassword(securePwd);

    //IP 생성
    String ip = RequestUtil.getRemoteAddr();

    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    String memberId = membersRepository.findMember(member.getEmail());
//    member.setRole("1");
    member.setId(memberId);
    member.setRegisterIp(ip);
    member.setRegisteredBy(memberId);
    member.setRegisteredAt(timestamp);
    member.setUpdatedAt(timestamp);
    member.setUpdaterIp(ip);
    member.setUpdatedBy(memberId);
    member.setVerified(verified.getStatus());

    new MembersDto(membersRepository.save(member.toEntity()));
//        String verifiedKey = UUID.randomUUID().toString(); // 회원 ID 생성

    return  membersRepository.userVerify(memberId);
    }

    // 이메일 URL 인증
    public void checkVerified(MembersDto member) throws Exception{

      // MembersDto
      comm_users memInfo = membersRepository.findByEmail(member.getEmail());

      Optional<comm_users_verified> verifiInfo = userVerifiedRepository.findByVerifiedId(memInfo.getId());

      //수정 시간(=현재시간)
      Timestamp timestamp = new Timestamp(System.currentTimeMillis());

      // 인증 번호 확인 member.getVerifiedKey()  프론트에서 받아온 값
      if(!member.getVerifiedKey().equals(verifiInfo.get().getVerifiedKey())) {
            throw new Exception(ApiResponseEnum.INVALID_ACCOUNT.getMessage());
      }

      //시작 시간
      Long registerTime = (memInfo.getRegisteredAt().getTime());

      //시간 차이
      Long differenceTime = timestamp.getTime() - registerTime;

      //제한 시간 10분 : 600000밀리초
      //3) 제한 시간 확인
      if (differenceTime > **********) {
        throw new Exception(ApiResponseEnum.TIMEOUT_EXPIRED.getMessage());
      }

      //이메일 인증 성공 저장
      memInfo.setUpdatedAt(timestamp);
      memInfo.setUpdatedBy(memInfo.getId());
      memInfo.setVerified("Y");

      verifiInfo.get().setStatus("Y");

      membersRepository.save(memInfo);
      userVerifiedRepository.save(verifiInfo.get());
    }

    //내 정보 수정
    @Transactional
    public MembersDto modifyInfo(MembersDto member) throws Exception {

    MembersDto memberInfo = new MembersDto(membersRepository.findById(member.getId()).get());

    // 2) 이메일 변경 여부 확인
    if (!member.getEmail().equals(memberInfo.getEmail())) {

      // 3) 이메일 중복 검사
      long result = dupleEmail(member.getEmail());
      if (result > 0) {
        throw new Exception(ApiResponseEnum.DUPLICATE_EMAIL.getMessage());
      }

      // 4) 이메일 유효성 검사
      Matcher checkEmail = CheckUtils.checkEmail(member.getEmail());
      if (!checkEmail.find()) {
        throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
      }

//      // 5) 인증 이메일 발송
//      String typeCd = "100001";
//      int templateId = 0;
//      String emailFrom = "<EMAIL>"; // 확인용
//      String emailTo = member.getEmail();
//      String subject = "이메일 인증 메일입니다.";
//      String content = emailService.modifyEmail(member);
//      String registeredBy = member.getEmail();
//      String registeredIp = RequestUtil.getRemoteAddr();
//      String updatedBy = member.getEmail();
//      String updatedIp = RequestUtil.getRemoteAddr();
//
//      EmailsendlogDto sendInfo = EmailsendlogDto.builder()
//        .typeCd(typeCd)
//        .templateId(templateId)
//        .emailFrom(emailFrom)
//        .emailTo(emailTo)
//        .subject(subject)
//        .content(content)
//        .registeredBy(registeredBy)
//        .registeredIp(registeredIp)
//        .updatedBy(updatedBy)
//        .updatedIp(updatedIp).build();
//
//      emailService.sendEmail(sendInfo);

    }

    // 6) 폰번호 유효성 체크
//    Matcher checkPhone = CheckUtils.checkPhone(member.getPhone());
//    if (!checkPhone.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_PHONE.getMessage());
//    }

    //IP 생성
    String ip = RequestUtil.getRemoteAddr();

    //수정 시간
    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    if (!member.getEmail()
      .equals(membersRepository.findById(member.getId()).get().getEmail())) {
      memberInfo.setVerified("N");
    }

//    memberInfo.setName(member.getName());
//    memberInfo.setPhone(member.getPhone());
    memberInfo.setEmail(member.getEmail());

//    memberInfo.setCompanyId(member.getCompanyId());
//    memberInfo.setDepartment(member.getDepartment());

    //수정시간 저장
//    memberInfo.setPasswordChangedAt(timestamp);
    memberInfo.setUpdatedAt(timestamp);
    memberInfo.setUpdatedBy(memberInfo.getId());
    memberInfo.setUpdaterIp(ip);

    membersRepository.save(memberInfo.toEntity());
    return membersRepository.selectInfo(memberInfo.getId());
  }

  // 내 정보 조회
  public MembersDto selectMember(MembersDto member) throws Exception {

    MembersDto memberInfo = new MembersDto(membersRepository.findById(member.getId()).get());

    // 2) 사용자 입력 비밀번호, DB에 암호화 된 비밀번호 비교
    if (!pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword())) {
      throw new Exception(ApiResponseEnum.NON_EXISTS_PASSWORD.getMessage());
    }
    return membersRepository.selectInfo(member.getId());
  }

  //비밀번호 변경
  public MembersDto updatePassword(MembersDto member) throws Exception {

//    // 2) 비밀번호 중복 확인
//    boolean result = checkPassword(member);
//    if (result == true) {
//      throw new Exception(ApiResponseEnum.PREVIOUS_PASSWORD.getMessage());
//    }

    // 3) 비밀번호 암호화 및 저장
    member.setPassword(pbkdf2PasswordEncoder.encode(member.getPassword()));

    MembersDto memInfo = new MembersDto(membersRepository.findById(member.getId()).get());

    //IP 생성
    String ip = RequestUtil.getRemoteAddr();

    //수정 시간
    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

//    if (memInfo.getPassword_1() != null) {
//      memInfo.setPassword_2(memInfo.getPassword_1());
//    }
//    memInfo.setPassword_1(memInfo.getPassword());
//    memInfo.setPassword(member.getPassword()); //입력 받은 값 저장

    //수정시간 저장
//    memInfo.setPasswordChangedAt(timestamp);
    memInfo.setUpdatedAt(timestamp);
    memInfo.setUpdatedBy(memInfo.getId());
    memInfo.setUpdaterIp(ip);

    return new MembersDto(membersRepository.save(memInfo.toEntity()));
  }

//  //비밀번호 변경 - 중복검사
//  public boolean checkPassword(MembersDto member) {
//
//    boolean result;
//
//    MembersDto memberInfo = new MembersDto(membersRepository.findById(member.getId()).get());
//
//    if (memberInfo.getPassword_1() == null) {
//      result = pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword());
//    } else {
//      if (memberInfo.getPassword_2() == null) {
//        result = pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword()) ||
//          pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword_1());
//      } else {
//        result = pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword()) ||
//          pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword_1()) ||
//          pbkdf2PasswordEncoder.matches(member.getPassword(), memberInfo.getPassword_2());
//      }
//    }
//    return result;
//  }

  //회원탈퇴
  @Transactional
  public MembersDto withdraw(MembersDto member) {

        Optional<comm_users> members = membersRepository.findById(member.getId());

    MembersDto memberInfo = new MembersDto(members.get()); //Optional<MembersDto> -> MembersDto 변환

    //IP 생성
    String ip = RequestUtil.getRemoteAddr();

    //수정 시간
    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    if (memberInfo != null) {
      memberInfo.setStatus("D");
      memberInfo.setUpdatedAt(timestamp);
      memberInfo.setUpdatedBy(memberInfo.getId());
      memberInfo.setUpdaterIp(ip);

    }
    return new MembersDto(membersRepository.save(memberInfo.toEntity()));
  }

  // 이메일 인증번호 전송
  @Transactional
  public void sendEmail(MembersDto member) throws Exception {
    userVerifiedRepository.deleteByEmail(member.getEmail());
    // temp 계정 삭제
    userVerifiedRepository.deleteByEmail(member.getEmail());
    membersRepository.deleteByEmail(member.getEmail());

    // 신규 ID 생성
    String id = UUID.randomUUID().toString();

    // id, email, 시간만 있는 임시 계정 생성
    member.setId(id);
    
    //수정 시간
    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    member.setRegisteredAt(timestamp);
    member.setRegisteredBy(member.getId());

    member.setUpdatedAt(timestamp);
    member.setUpdatedBy(member.getId());

    MembersDto memberInfo = new MembersDto(membersRepository.save(member.toEntity()));

    // 4) 인증번호 생성
    String otp = PasswordUtils.tempPassword(6);

    // 5) 인증번호 저장
    UserVerifiedDto userVerifiedDto = new UserVerifiedDto();
    userVerifiedDto.setVerifiedKey(otp);
    userVerifiedDto.setStatus("U"); // 미확인 Unverified
    userVerifiedDto.setUserId(id);  // member의 id값으로 저장(fk)

    userVerifiedRepository.save(userVerifiedDto.toEntity());

    // 6) 이메일 전송
    String subject = "이메일 인증번호 안내 메일입니다.";
    String content = emailService.sendOtp(otp);

    MimeMessage mimeMessage = javaMailSender.createMimeMessage();
    MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, false, "UTF-8");
    mimeMessageHelper.setTo(member.getEmail()); // 메일 수신자
    mimeMessageHelper.setSubject(subject); // 메일 제목
    mimeMessageHelper.setText(content, true); // 메일 본문 내용, HTML 여부
    javaMailSender.send(mimeMessage);

  }

  // 이메일 인증번호 재전송
  @Transactional
  public void resendEmail(MembersDto member) throws Exception {
    // 1) 회원정보 조회
    comm_users memInfo = membersRepository.findByEmail(member.getEmail());

    if(memInfo == null){
      throw new Exception(ApiResponseEnum.NON_EXISTS_EMAIL.getMessage());
    }
    // 4) 인증번호 생성
    String otp = PasswordUtils.tempPassword(6);

    // 5) 인증번호 저장
    UserVerifiedDto userVerifiedDto = new UserVerifiedDto();
    userVerifiedDto.setVerifiedKey(otp);
    userVerifiedDto.setStatus("U"); // 미확인 Unverified
    userVerifiedDto.setUserId(memInfo.getId());  // member의 id값으로 저장(fk)

    userVerifiedRepository.save(userVerifiedDto.toEntity());

    //수정 시간 update
    Timestamp timestamp = new Timestamp(System.currentTimeMillis());

    memInfo.setRegisteredAt(timestamp);
    memInfo.setUpdatedAt(timestamp);

    membersRepository.save(memInfo);

    // 6) 이메일 전송
    String subject = "이메일 인증번호 안내 메일입니다.";
    String content = emailService.sendOtp(otp);

    MimeMessage mimeMessage = javaMailSender.createMimeMessage();
    MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMessage, false, "UTF-8");
    mimeMessageHelper.setTo(member.getEmail()); // 메일 수신자
    mimeMessageHelper.setSubject(subject); // 메일 제목
    mimeMessageHelper.setText(content, true); // 메일 본문 내용, HTML 여부
    javaMailSender.send(mimeMessage);
  }

//  // 이메일 인증번호 검증
//  public String memlog(OtpsendlogDto otpsendlog) throws Exception {
//
//    Optional<comm_log_otpsend> otpsendlogInfo = Optional.of(otpsendlogRepository.findByIdlog(otpsendlog.getId()).get());
//
//    // MembersDto
//    Optional<comm_users> memberInfo = membersRepository.findById(otpsendlogInfo.get().getUserId());
//
//    // DB OTP
//    String memOtp = otpsendlogRepository.findByIdlog(otpsendlog.getId()).get().getOtpPin();
//
//    // OTP INFO
//    OtpsendlogDto otpInfo = new OtpsendlogDto(otpsendlogRepository.findByIdlog(otpsendlog.getId()).get());
//
//    //IP 생성
//    String ip = RequestUtil.getRemoteAddr();
//
//    //수정 시간(=현재시간)
//    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
//
//    // 2) OTP일치 확인
//    if (!memOtp.equals(otpsendlog.getOtpPin())) {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//    //시작 시간
//    Long registerTime = (otpInfo.getRegisteredAt().getTime());
//
//    //시간 차이
//    Long differenceTime = timestamp.getTime() - registerTime;
//
//    //제한 시간 10분 30초 : 630000밀리초
//    //3) 제한 시간 확인
//    if (differenceTime > 630000) {
//      throw new Exception(ApiResponseEnum.TIMEOUT_EXPIRED.getMessage());
//    }
//    //등록시간 저장
//    memberInfo.get().setUpdatedAt(timestamp);
//    memberInfo.get().setUpdatedBy(memberInfo.get().getId());
//    memberInfo.get().setUpdaterIp(ip);
//
//    memberInfo.get().setVerified("Y");
//
//    membersRepository.save(memberInfo.get());
//
//    //otp관련 수정시간 저장
//    otpsendlogInfo.get().setVerified("Y");
//    otpsendlogInfo.get().setUpdatedAt(timestamp);
//    otpsendlogInfo.get().setUpdatedBy(memberInfo.get().getId());
//    otpsendlogInfo.get().setUpdaterIp(ip);
//
//    otpsendlogRepository.save(otpsendlogInfo.get());
//
//    return memOtp;
//  }

//  //회원 아이디 찾기 : 이름,폰번호
//  public String searchId(MembersDto member) throws Exception {
//
//    // 전화번호 유효성 체크
//    Matcher checkPhone = CheckUtils.checkPhone(member.getPhone());
//    if (!checkPhone.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_PHONE.getMessage());
//    }
//
//    // 저장 된 전화번호, 이름 일치여부 확인
//    if (membersRepository.findByNameAndPhone(member.getName(), member.getPhone()) == null) {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//
//    // 이메일 마스킹 처리 및 반환
//    String memInfo = new MembersDto(membersRepository.findByNameAndPhone(member.getName(), member.getPhone())).getEmail();
//    String maskingEmail = (memInfo.replaceAll("(?<=.{3}).(?=.*@)", "*"));
//
//    return maskingEmail;
//  }

//  //비밀번호 찾기
//  public MembersDto memPassword(MembersDto member)
//    throws Exception {
//
//    // 2) 이메일 유효성 검사
//    Matcher checkEmail = CheckUtils.checkEmail(member.getEmail());
//    if (!checkEmail.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
//    }
//
//    // 3) 이메일 존재 여부 확인
//    comm_users memInfo = membersRepository.findByEmail(member.getEmail());
//
//    if (memInfo == null) {
//      throw new Exception(ApiResponseEnum.NON_EXISTS_EMAIL.getMessage());
//    }
//
//    // 4) 임시 비밀번호 생성
//    String tempPassword = PasswordUtils.tempPassword(10);
//
//    // 5) 임시 비밀번호 암호화
//    String MemPassword = pbkdf2PasswordEncoder.encode(tempPassword);
//
//    MembersDto memberInfo = new MembersDto(membersRepository.findByEmail(member.getEmail()));
//    memberInfo.setPassword(MemPassword);
//
//    //IP 생성
//    String ip = RequestUtil.getRemoteAddr();
//
//    //수정 시간
//    Timestamp timestamp = new Timestamp(System.currentTimeMillis());
//
//    //수정시간 저장
////    memberInfo.setPasswordChangedAt(timestamp);
//    memberInfo.setUpdatedAt(timestamp);
//    memberInfo.setUpdatedBy(memberInfo.getId());
//    memberInfo.setUpdaterIp(ip);
//
//    // 5) 임시 비밀번호 안내 메일 전송(tempPassword)
//    String typeCd = "100002";
//    int templateId = 0;
//    String emailFrom = "<EMAIL>"; //확인용
//    String emailTo = memberInfo.getEmail();
//    String subject = "임시 비밀번호 안내 메일입니다.";
//    String content = emailService.resetPassword(tempPassword);
//    String registeredBy = memberInfo.getEmail();
//    String registeredIp = ip;
//    String updatedBy = memberInfo.getEmail();
//    String updatedIp = ip;
//
//    EmailsendlogDto sendInfo = EmailsendlogDto.builder()
//      .typeCd(typeCd)
//      .templateId(templateId)
//      .emailFrom(emailFrom)
//      .emailTo(emailTo)
//      .subject(subject)
//      .content(content)
//      .registeredBy(registeredBy)
//      .registeredIp(registeredIp)
//      .updatedBy(updatedBy)
//      .updatedIp(updatedIp).build();
//
//    emailService.sendEmail(sendInfo);
//
//
//    return new MembersDto(membersRepository.save(memberInfo.toEntity()));
//  }

//  // 서비스 관리자 > 회원 관리
//  // 회원 목록 / 검색
//  public List<MembersDto> selectList(String department, String name, String email) {
//    return membersRepository.selectList(department, name, email);
//  }
//
//  // 회원 정보 조회
//  public Optional<MembersDto> selectMemberInfo(String id) {
//    return Optional.of(membersRepository.selectInfo(id));
//  }
//
//  // 회원 정보 수정
//  public MembersDto updateMember(MembersDto changeInfo) throws Exception {
//
//    // 회원 기존 정보 조회
//    MembersDto memberInfo = selectMemberInfo(changeInfo.getId()).get();
//
//    // 2) 이메일 변경 여부 확인
//    if (!changeInfo.getEmail().equals(memberInfo.getEmail())) {
//
//      // 3) 이메일 중복 검사
//      Long dupleEmail = dupleEmail(changeInfo.getEmail());
//      if (dupleEmail > 0) {
//        throw new Exception(ApiResponseEnum.DUPLICATE_EMAIL.getMessage());
//      }
//
//      // 4) 이메일 유효성 검사
//      Matcher emailMatcher = CheckUtils.checkEmail(changeInfo.getEmail());
//      if (!emailMatcher.find()) {
//        throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
//      }

//      // 5) 이메일 인증 메일 전송
//      String typeCd = "100001";
//      int templateId = 0;
//      String emailFrom = "emailFrom";
//      String emailTo = changeInfo.getEmail();
//      String subject = "이메일 인증 메일입니다.";
//      String content = emailService.modifyEmail(changeInfo);
//      String registeredBy = "로그인한 사용자";
//      String registeredIp = RequestUtil.getRemoteAddr();
//      String updatedBy = "로그인한 사용자";
//      String updatedIp = RequestUtil.getRemoteAddr();
//
//      EmailsendlogDto sendInfo = EmailsendlogDto.builder()
//        .typeCd(typeCd)
//        .templateId(templateId)
//        .emailFrom(emailFrom)
//        .emailTo(emailTo)
//        .subject(subject)
//        .content(content)
//        .registeredBy(registeredBy)
//        .registeredIp(registeredIp)
//        .updatedBy(updatedBy)
//        .updatedIp(updatedIp).build();
//
//      emailService.sendEmail(sendInfo);
//    }
//
//    // 6) 휴대폰 번호 유효성 검사
//    Matcher phoneMatcher = CheckUtils.checkPhone(changeInfo.getPhone());
//    if (!phoneMatcher.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_PHONE.getMessage());
//    }
//
//    // 7) 유효성 검사 모두 통과 시 회원 정보 수정
//    String updaterIp = RequestUtil.getRemoteAddr();                   // 회원 정보 수정 IP
//    String updatedBy = "로그인한 서비스 관리자의 ID";                     // 수정 작업자 ID
//    Timestamp updatedAt = new Timestamp(System.currentTimeMillis());  // 변경 일시
//
//    memberInfo.setName(changeInfo.getName());
//    if (!changeInfo.getEmail().equals(memberInfo.getEmail())) {
//      memberInfo.setVerified("N");
//    }
//    memberInfo.setEmail(changeInfo.getEmail());
//    memberInfo.setPhone(changeInfo.getPhone());
////    memberInfo.setCompanyId(changeInfo.getCompanyId());
////    memberInfo.setDepartment(changeInfo.getDepartment());
////    memberInfo.setRole(changeInfo.getRole());
//    memberInfo.setUpdaterIp(updaterIp);
//    memberInfo.setUpdatedBy(updatedBy);
//    memberInfo.setUpdatedAt(updatedAt);
//
//    membersRepository.save(memberInfo.toEntity());
//    return membersRepository.selectInfo(changeInfo.getId());
//  }
//
//  // 비밀번호 초기화
//  public void resetPassword(String id) throws Exception {
//
//    // 회원 기존 정보 조회
//    Optional<comm_users> membersEntity = membersRepository.findById(id);
//
//    // 2) 임시 비밀번호 생성
//    String tempPassword = PasswordUtils.tempPassword(10);
//
//    // 3) 임시 비밀번호 암호화
//    String securePassword = pbkdf2PasswordEncoder.encode(tempPassword);
//
//    if (membersEntity.isPresent()) {
//      // 4) 임시 비밀번호 저장
//      MembersDto memberInfo = new MembersDto(membersEntity.get());
//
//      Timestamp timestamp = new Timestamp(System.currentTimeMillis()); // 변경 일시
//      String updaterIp = RequestUtil.getRemoteAddr();                  // 회원 정보 수정 IP
//
//      // password
//      String password = memberInfo.getPassword();
//
//      // password_1
////      String password1 = memberInfo.getPassword_1();
//
////      if (password1 != null) {
////        memberInfo.setPassword_2(password1);
////      }
//      memberInfo.setPassword(securePassword);
////      memberInfo.setPassword_1(password);
////      memberInfo.setPasswordChangedAt(timestamp);
//      memberInfo.setUpdatedAt(timestamp);
//      memberInfo.setUpdaterIp(updaterIp);
//      memberInfo.setUpdatedBy("로그인한 서비스 관리자");
//
//      membersRepository.save(memberInfo.toEntity());
//
//      // 5) 임시 비밀번호 안내 메일 전송(tempPassword)
//      String typeCd = "100002";
//      int templateId = 0;
//      String emailFrom = "emailFrom";
//      String emailTo = memberInfo.getEmail();
//      String subject = "임시 비밀번호 안내 메일입니다.";
//      String content = emailService.resetPassword(tempPassword);
//      String registeredBy = "로그인한 사용자";
//      String registeredIp = RequestUtil.getRemoteAddr();
//      String updatedBy = "로그인한 사용자";
//      String updatedIp = RequestUtil.getRemoteAddr();
//
//      EmailsendlogDto sendInfo = EmailsendlogDto.builder()
//        .typeCd(typeCd)
//        .templateId(templateId)
//        .emailFrom(emailFrom)
//        .emailTo(emailTo)
//        .subject(subject)
//        .content(content)
//        .registeredBy(registeredBy)
//        .registeredIp(registeredIp)
//        .updatedBy(updatedBy)
//        .updatedIp(updatedIp).build();
//
//      emailService.sendEmail(sendInfo);
//    } else {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//  }
//
//  // 회원 가입 승인 / 거절
//  public void updateApproved(List<String> memList, String approved) throws Exception {
//    // 2) 회원 가입 승인 / 거절 저장
//    Timestamp updatedAt = new Timestamp(System.currentTimeMillis());  // 변경 일시
//    String updaterIp = RequestUtil.getRemoteAddr();                   // 회원 정보 수정 IP
//    String updatedBy = "로그인한 서비스 관리자";                          // 수정 작업자 ID
//    String status = "N";
//    if (approved.equals("N")) { // 거절할 경우 status를 R로 변경
//      status = "R";
//    }
//
//    int result = membersRepository.updateApproved(memList, approved, status, updatedAt, updaterIp, updatedBy);
//    if (result != memList.size()) {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//  }
//
//  // 회원 잠금
//  public void lockMember(List<String> memList) throws Exception {
//
//    // 2) 회원 잠금
//    Timestamp updatedAt = new Timestamp(System.currentTimeMillis());  // 변경 일시
//    String updaterIp = RequestUtil.getRemoteAddr();                   // 회원 정보 수정 IP
//    String updatedBy = "로그인한 서비스 관리자";                          // 수정 작업자 ID
//
//    int result = membersRepository.updateStatus(memList, updatedAt, updaterIp, updatedBy);
//    if (result != memList.size()) {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//  }
//
//    // 회원 등록
//    public void insertMember(MembersDto memberInfo) throws Exception {
//
//    // 2) 이메일 중복 검사
//    Long dupleEmail = dupleEmail(memberInfo.getEmail());
//    if (dupleEmail > 0) {
//      throw new Exception(ApiResponseEnum.DUPLICATE_EMAIL.getMessage());
//    }
//
//    // 3) 이메일 유효성 검사
//    Matcher checkEmail = CheckUtils.checkEmail(memberInfo.getEmail());
//    if (!checkEmail.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
//    }
//
//    // 4) 전화번호 유효성 검사
//    Matcher checkPhone = CheckUtils.checkPhone(memberInfo.getPhone());
//    if (!checkPhone.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_PHONE.getMessage());
//    }
//
//    // 5) 비밀번호 암호화
//    String securePassword = pbkdf2PasswordEncoder.encode(memberInfo.getPassword());
//
//    // 6) 회원 등록 정보 저장
//    String memberId = UUID.randomUUID().toString(); // 회원 ID 생성
//    String ip = RequestUtil.getRemoteAddr();        // 회원 등록 IP 생성
//    String id = "로그인한 서비스 관리자의 ID";          // 가입 작업자 ID
//
//    memberInfo.setId(memberId);
//    memberInfo.setPassword(securePassword);
////    memberInfo.setRole("1");
//    memberInfo.setRegisterIp(ip);
//    memberInfo.setUpdaterIp(ip);
//    memberInfo.setRegisteredBy(id);
//    memberInfo.setUpdatedBy(id);
//
////    if (memberInfo.getApproved().equals("Y")) {
////      memberInfo.setStatus("N");
////    }
//    membersRepository.save(memberInfo.toEntity());
//  }
//
//  // 이메일 중복 검사
//  public void dupleEmail(Map<String, String> email) throws Exception {
//    // 2) 이메일 유효성 검사
//    Matcher checkEmail = CheckUtils.checkEmail(email.get("email"));
//    if (!checkEmail.find()) {
//      throw new Exception(ApiResponseEnum.INVALID_EMAIL.getMessage());
//    }
//
//    // 3) 이메일 중복 검사
//    Long dupleEmail = dupleEmail(email.get("email"));
//    if (dupleEmail > 0) {
//      throw new Exception(ApiResponseEnum.DUPLICATE_EMAIL.getMessage());
//    }
//  }

//  // 회원 초대
//  public void inviteMember(List<String> memberList) throws Exception {
//    // 2) 로그인 이력 확인
//    Long count = membersRepository.countByCheckLoginEqualsAndIdIn("Y", memberList);
//
//    if (count == 0) {
//      for (int i = 0; i < memberList.size(); i++) {
//        String memberEmail = membersRepository.findById(memberList.get(i)).get().getEmail();
//
//        // 3) 초대 메일 보내기
//        String typeCd = "100005";
//        int templateId = 0;
//        String emailFrom = "emailFrom";
//        String emailTo = memberEmail;
//        String subject = "초대 이메일 입니다.";
//        String content = emailService.inviteAdmin();
//        String registeredBy = "로그인한 서비스 관리자";
//        String registeredIp = RequestUtil.getRemoteAddr();
//        String updatedBy = "로그인한 서비스 관리자";
//        String updatedIp = RequestUtil.getRemoteAddr();
//
//        EmailsendlogDto sendInfo = EmailsendlogDto.builder()
//          .typeCd(typeCd)
//          .templateId(templateId)
//          .emailFrom(emailFrom)
//          .emailTo(emailTo)
//          .subject(subject)
//          .content(content)
//          .registeredBy(registeredBy)
//          .registeredIp(registeredIp)
//          .updatedBy(updatedBy)
//          .updatedIp(updatedIp).build();
//
//        emailService.sendEmail(sendInfo);
//      }
//    } else {
//      throw new Exception(ApiResponseEnum.NO_SEND_INVITE_EMAIL.getMessage());
//    }
//  }

  // 이메일 중복 검사
  public Long dupleEmail(String email) {
    return membersRepository.countByEmail(email);
  }

//  // 회원 삭제 (탈퇴 처리)
//  public void deleteMem(List<String> memList) throws Exception {
//
//    Timestamp timestamp = new Timestamp(System.currentTimeMillis());  // 변경 일시
//    String updaterIp = RequestUtil.getRemoteAddr();                   // 회원 정보 수정 IP
//    String updatedBy = "로그인한 서비스 관리자";                          // 수정 작업자 ID
//
//    int result = membersRepository.deleteById(memList, timestamp, updaterIp, updatedBy);
//    if (result != memList.size()) {
//      throw new Exception(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage());
//    }
//  }

  public MembersDto getUserDetailsByEmail(String userName) {
    comm_users userEntity = membersRepository.findByEmail(userName);

    if (userEntity == null) {
      throw new UsernameNotFoundException(userName);
    }

    return new ModelMapper().map(userEntity, MembersDto.class);
  }
}