package kr.co.digitalzone.member.repository;

import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import kr.co.digitalzone.dto.MembersDto;
import kr.co.digitalzone.entity.comm_users;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MembersRepository extends JpaRepository<comm_users, String> {
   // 회원가입
   // 회원가입
   @Query("select new kr.co.digitalzone.dto.MembersDto(cu.id, cu.email, cu.verified, cu.name, cu.phone,"
           + "cu.password,"
           + "cu.registeredAt, cu.updatedAt, cu.registerIp, cu.updaterIp, cu.registeredBy, cu.updatedBy,"
           + "cu.status) from comm_users cu where cu.id = :userId")
   MembersDto selectInfo(@Param("userId") String id);

    // 회원가입 url 인증
    @Query("select new kr.co.digitalzone.dto.MembersDto(cu.id, cu.email, cv.verifiedKey) from comm_users cu join comm_users_verified cv on cu.id = cv.userId where cu.id = :userId")
    MembersDto userVerify(@Param("userId")String id);

    // 아이디 찾기
    Optional<comm_users> findById(String id);

    // 아이디 찾기 : 폰번호, 이름
    comm_users findByNameAndPhone(String name, String phone);

    // 비밀번호 찾기, 이메일 중복 검사
    @Query("select count(*) from comm_users where email = :email and password is not null")
    Long countByEmail(String email);

    // password null 인 경우 계정 삭제
    @Modifying
    @Query("delete from comm_users where email=:email and password is null")
    void deleteByEmail(String email);

    // 이메일 인증 여부
    comm_users findByEmail(String email);

    // 로그인 이메일 2차 인증
    @Query("select m.id from comm_users m where m.email=:email")
    String findMember(@Param("email") String email);

    // 회원 초대
//    Long countByCheckLoginEqualsAndIdIn(String checkLogin, List<String> memberList);

//    // 회원 가입 승인
//    @Transactional
//    @Modifying
//    @Query("update comm_users set approved = :approved, status = :status, updatedAt = :updatedAt, updaterIp = :updaterIp, updatedBy = :updatedBy where id in (:id)")
//    int updateApproved(@Param("id") List<String> memList, @Param("approved") String approved,
//        @Param("status") String status, @Param("updatedAt") Timestamp updatedAt,
//        @Param("updaterIp") String updaterIp, @Param("updatedBy") String updatedBy);

//    // 회원 잠금
//    @Transactional
//    @Modifying
//    @Query("update comm_users set status = 'L', updatedAt = :updatedAt, updaterIp = :updaterIp, updatedBy = :updatedBy where id in (:id)")
//    int updateStatus(@Param("id") List<String> memList, @Param("updatedAt") Timestamp updatedAt,
//        @Param("updaterIp") String updaterIp, @Param("updatedBy") String updatedBy);
//
//    // 회원 삭제 (탈퇴 처리)
//    @Transactional
//    @Modifying
//    @Query("update comm_users set status = 'D', updatedAt = :timestamp, updaterIp = :updaterIp, updatedBy = :updatedBy where id in (:id)")
//    int deleteById(@Param("id") List<String> memList, @Param("timestamp") Timestamp timestamp,
//        @Param("updaterIp") String updaterIp, @Param("updatedBy") String updatedBy);

}