//package kr.co.digitalzone.member.repository;
//
//import com.querydsl.core.types.Projections;
//import com.querydsl.core.types.dsl.BooleanExpression;
//import com.querydsl.jpa.impl.JPAQueryFactory;
//import java.util.List;
//import kr.co.digitalzone.dto.MembersDto;
//import kr.co.digitalzone.entity.Qcomm_company;
//import kr.co.digitalzone.entity.Qcomm_users;
//import lombok.RequiredArgsConstructor;
//
//@RequiredArgsConstructor
//public class MembersRepositoryImpl implements MembersRepositoryCustom {
//    private final JPAQueryFactory jpaQueryFactory;
//    Qcomm_users commUsers = Qcomm_users.comm_users;
//    Qcomm_company commCompany = Qcomm_company.comm_company;
//
////    @Override
////    public List<MembersDto> selectList(String department, String name, String email) {
////        return jpaQueryFactory
////            .select(Projections.constructor(MembersDto.class, commUsers.id, commUsers.email, commUsers.verified,
////                commUsers.approved, commUsers.name, commUsers.phone, commUsers.password, commUsers.password_1,
////                commUsers.password_2, commUsers.passwordChangedAt, commUsers.registeredAt, commUsers.updatedAt,
////                commUsers.registerIp, commUsers.updaterIp, commUsers.registeredBy, commUsers.updatedBy, commUsers.status,
////                commUsers.companyId, commCompany.companyName, commUsers.department, commCompany.companyPhone,
////                commUsers.checkLogin, commUsers.role))
////            .from(commUsers)
////            .join(commCompany).on(commUsers.companyId.eq(commCompany.companyId))
////            .where(departmentEq(department), nameEq(name), emailEq(email), commUsers.role.eq("1"))
////            .fetch();
////    }
//
//    private BooleanExpression departmentEq(String department) {
//        if(department == null) {
//            return null;
//        }
//        return commUsers.department.eq(department);
//    }
//
//    private BooleanExpression nameEq(String name) {
//        if(name == null) {
//            return null;
//        }
//        return commUsers.name.eq(name);
//    }
//
//    private BooleanExpression emailEq(String email) {
//        if(email == null) {
//            return null;
//        }
//        return commUsers.email.eq(email);
//    }
//}
