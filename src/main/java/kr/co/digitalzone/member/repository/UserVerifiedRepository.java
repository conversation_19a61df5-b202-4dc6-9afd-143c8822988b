package kr.co.digitalzone.member.repository;

import java.util.Optional;
import kr.co.digitalzone.entity.comm_users_verified;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface UserVerifiedRepository extends JpaRepository<comm_users_verified, String> {

  // 이메일 url인증
  @Query("select cv from comm_users_verified cv join comm_users cu on(cv.userId = cu.id) where cv.userId=:id")
  Optional<comm_users_verified> findByVerifiedId (@Param("id") String id);

  @Query("select cv from comm_users cu join comm_users_verified cv on(cv.userId = cu.id) where cu.email=:email")
  comm_users_verified findByEmail(@Param("email") String email);

  @Modifying
  // comm_users 의 email 값이 comm_users_verified 의 userId 값과 같은 행을 삭제
  @Query("delete from  comm_users_verified where userId in (select id from comm_users where email = :email)")
  void deleteByEmail(@Param("email") String  email);

}
