//package kr.co.digitalzone.member.repository;
//
//import java.util.List;
//import java.util.Optional;
//import kr.co.digitalzone.entity.comm_log_otpsend;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.repository.query.Param;
//
//public interface OtpsendlogRepository extends JpaRepository<comm_log_otpsend, Integer> {
//
//  //이메일 인증1
//  List<comm_log_otpsend> findByUserId(String id);
//
//  //이메일 인증2
//  @Query("select o from comm_log_otpsend o join comm_users m on(o.userId = m.id) where o.id=:id")
//  Optional<comm_log_otpsend> findByIdlog(@Param("id") Integer id);
//
//}
