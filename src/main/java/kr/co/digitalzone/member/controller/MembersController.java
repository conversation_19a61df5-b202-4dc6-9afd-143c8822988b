package kr.co.digitalzone.member.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.co.digitalzone.dto.MembersDto;
//import kr.co.digitalzone.dto.OtpsendlogDto;
import kr.co.digitalzone.dto.UserVerifiedDto;
import kr.co.digitalzone.member.service.MembersService;
import kr.co.digitalzone.response.ApiResponse;
import kr.co.digitalzone.response.ApiResponseEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.python.core.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Tag( name= "로그인 및 회원가입", description = "회원가입 내 관련 API")
@RequiredArgsConstructor
@Slf4j
public class MembersController {
    public static final Logger logger = LoggerFactory.getLogger(MembersController.class);
    private final MembersService membersService;
    /**
     * 이메일 중복 검사
     * 1) input - eamil
     * 2) 이메일 유효성 검사
     * 3) 이메일 중복 여부 확인
     * 4) 성공시 OK 리턴
     */
    @Operation(summary = "이메일 인증", description = "이메일 인증 버튼을 누르면 메일 사용가능 유무 확인 후 해당 이메일로 인증번호 발송")
    @PostMapping("/login/dupliemail")
    public ApiResponse duplicateMemEmail(@RequestBody MembersDto member) {

        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();
        try {
            // 성공시 OK 리턴
            membersService.duplicateEmail(member);

            // 인증 이메일 전송
            membersService.sendEmail(member);
        } catch (Exception e) {
            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_EMAIL.getMessage())) {
                code = ApiResponseEnum.DUPLICATE_EMAIL.getCode();
                message = ApiResponseEnum.DUPLICATE_EMAIL.getMessage();
            }
            if (e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
                code = ApiResponseEnum.INVALID_EMAIL.getCode();
                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
            }
            e.printStackTrace();
        }
        return ApiResponse.res(code, message);
    }

    /**
     * 이메일 재전송
     * 1) input - eamil
     */
//    @PostMapping("/user/resendemail") //
//    public ApiResponse resendMemEmail(@RequestParam("email") String email) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        MembersDto member = new MembersDto();
//        member.setEmail(email);
//
//        try {
//            // 인증 이메일 재전송
//            membersService.resendEmail(member);
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.NON_EXISTS_EMAIL.getMessage())) {
//                code = ApiResponseEnum.NON_EXISTS_EMAIL.getCode();
//                message = ApiResponseEnum.NON_EXISTS_EMAIL.getMessage();
//            }
//
//            e.printStackTrace();
//        }
//        return ApiResponse.res(code, message);
//    }

    /** 회원 가입
     * 1) input - email, password, phone,name, companyId, department
     * 2) 이메일 중복 검사
     * 3) 이메일 유효성 체크
     * 4) 비밀번호 암호화
     * 5) 전화번호 유효성 체크
     * 6) 인증 이메일 전송
     * 7) 성공시 OK 리턴
     * */
    @PostMapping("/login/regist")
    @Operation(summary ="회원가입", description = "회원가입 ")
    public ApiResponse<MembersDto> joinMembers(@RequestBody MembersDto member) {

        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();
        MembersDto data = null;

        try {
            // 성공시 OK 리턴 (프론트 요청으로 id,email 출력)
            data = membersService.joinMembers(member);

        } catch (Exception e) {
            if (e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
                code = ApiResponseEnum.INVALID_EMAIL.getCode();
                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
            }
            if(e.getMessage().equals(ApiResponseEnum.UNVERIFIED_EMAIL.getMessage())){
                code = ApiResponseEnum.UNVERIFIED_EMAIL.getCode();
                message = ApiResponseEnum.UNVERIFIED_EMAIL.getMessage();
            }
            e.printStackTrace();
        }
        return ApiResponse.res(code, message, data);
    }

    /** 이메일 인증번호 인증
     * 1) input - , userEmail, userVerified(이메일, 인증번호)
     * 2) 이메일인증여부 인증으로 수정
     * 3) 성공시 OK 리턴
     * */
    @PostMapping("/login/checkVerified")
    @Operation(summary= "이메일 인증번호 인증 확인", description = "인증번호 입력후 확인 버튼 클릭시 인증여부 확인")
    public ApiResponse checkVerified(@RequestParam("email") String email, @RequestParam("verifiedKey") String verifiedKey ) {

        int code = ApiResponseEnum.OK.getCode();
        String message = ApiResponseEnum.OK.getMessage();

        MembersDto member = new MembersDto();
        member.setEmail(email);
        member.setVerifiedKey(verifiedKey);

        try {
            // 성공시 OK 리턴
            membersService.checkVerified(member);
        } catch (Exception e) {
            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
            }
            if (e.getMessage().equals(ApiResponseEnum.INVALID_ACCOUNT.getMessage())) {
                code = ApiResponseEnum.INVALID_ACCOUNT.getCode();
                message = ApiResponseEnum.INVALID_ACCOUNT.getMessage();
            }
            e.printStackTrace();
        }
        return ApiResponse.res(code, message);
    }

    /** 이메일 인증번호 전송 (otp) : 추후 사용 예정
     * 1) input - id, email
     * 2) 이메일 존재 여부 체크
     * 3) status 상태 N(정상) 및 W(대기) 확인
     * 4) OTP 생성
     * 5) OTP 저장
     * 6) otp 이메일 전송
     * 7) 성공시 OK 출력
     * */
//    @PostMapping("/user/certificationemail")
//    public ApiResponse<Integer> checkEmail(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 성공시 OK 출력 (프론트 요청 : otpsend id값 출력)
//            membersService.sendEmail(member);
//
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.NON_EXISTS_EMAIL.getMessage())) {
//                code = ApiResponseEnum.NON_EXISTS_EMAIL.getCode();
//                message = ApiResponseEnum.NON_EXISTS_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.NO_SEND_EMAIL.getMessage())) {
//                code = ApiResponseEnum.NO_SEND_EMAIL.getCode();
//                message = ApiResponseEnum.NO_SEND_EMAIL.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message);
//    }

    /** 이메일 인증번호 검증
     * 1) input - (otp table) id, otp_pin
     * 2) OTP일치 확인
     * 3) 제한 시간 확인
     * 4) 성공하면 OK 리턴
     * */
//    @PostMapping("/user/checkotp")
//    public ApiResponse checkOtp(@RequestBody OtpsendlogDto otpsendlog) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            //성공하면 OK 리턴
//            membersService.memlog(otpsendlog);
//
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.TIMEOUT_EXPIRED.getMessage())) {
//                code = ApiResponseEnum.TIMEOUT_EXPIRED.getCode();
//                message = ApiResponseEnum.TIMEOUT_EXPIRED.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message);
//    }

    /**비밀번호 찾기
     * 1) input - email
     * 2) 이메일 유효성 체크
     * 3) 이메일 존재 여부 체크
     * 4) 임시 비밀번호 생성
     * 5) 임시 비밀번호 암호화
     * 6) 암호화 된 임시 비밀번호 저장 및 이메일 전송
     * 7) 성공시 OK 리턴
     * */
//    @PostMapping("/user/findpassword")
//    public ApiResponse searchMemPassword(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 성공시 OK 리턴
//            membersService.memPassword(member);
//
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.NON_EXISTS_EMAIL.getMessage())) {
//                code = ApiResponseEnum.NON_EXISTS_EMAIL.getCode();
//                message = ApiResponseEnum.NON_EXISTS_EMAIL.getMessage();
//            }
//            if(e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
//                code = ApiResponseEnum.INVALID_EMAIL.getCode();
//                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message);
//    }

    /**아이디 찾기
     * 1) input - name,phone
     * 2)전화번호 유효성 체크
     * 3)저장 된 전화번호, 이름 일치여부 확인
     * 4)이메일 마스킹 처리 및 반환
     * 5)성공시 OK 리턴
     * */
//    @PostMapping("/user/findid")
//    public ApiResponse<String> searchId(@RequestBody MembersDto member){
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//        String data = null;
//
//        try {
//            //성공시 OK 리턴
//            data = membersService.searchId(member);
//
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.INVALID_PHONE.getMessage())) {
//                code = ApiResponseEnum.INVALID_PHONE.getCode();
//                message = ApiResponseEnum.INVALID_PHONE.getMessage();
//            }
//            if(e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message, data);
//    }

    /** 비밀번호 변경
     * 1) input - id, password
     * 2) 비밀번호 중복 확인
     * 3) 비밀번호 암호화 및 저장
     * 4) 성공시 OK 리턴
     * */
//    @PostMapping("/user/chgpassword")
//    public ApiResponse updatePassword(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 성공시 OK 리턴
//            membersService.updatePassword(member);
//
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.PREVIOUS_PASSWORD.getMessage())) {
//                code = ApiResponseEnum.PREVIOUS_PASSWORD.getCode();
//                message = ApiResponseEnum.PREVIOUS_PASSWORD.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message);
//    }

    /** 내 정보 조회
     * 1) input - id, pssword
     * 2) 사용자 입력 비밀번호, DB에 암호화 된 비밀번호 비교
     * 3) 성공시 MembersDto 리턴
     * */
//    @PostMapping("/user/mypage")
//    public ApiResponse<MembersDto> userMypage(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//        MembersDto data = null;
//
//        try {
//            //성공시 MembersDto 리턴
//            data = membersService.selectMember(member).toApiResponse();
//
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.NON_EXISTS_PASSWORD.getMessage())) {
//                code = ApiResponseEnum.NON_EXISTS_PASSWORD.getCode();
//                message = ApiResponseEnum.NON_EXISTS_PASSWORD.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message, data);
//    }

    /** 내 정보 수정
     * 1) input - email, phone, name, id, companyId, department
     * 2) 이메일 변경 여부 확인
     * 3) 이메일 중복 검사
     * 4) 이메일 유효성 검사
     * 5) 인증 이메일 발송
     * 6) 폰번호 유효성 체크
     * 7) 성공시 OK 리턴
     * */
//    @PostMapping("/user/chginfo")
//    public ApiResponse<MembersDto> modifyInfo(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//        MembersDto data = null;
//
//        try {
//            // 성공시 OK 리턴
//            data = membersService.modifyInfo(member).toApiResponse();
//
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
//                code = ApiResponseEnum.INVALID_EMAIL.getCode();
//                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
//            }
//            if(e.getMessage().equals(ApiResponseEnum.INVALID_PHONE.getMessage())) {
//                code = ApiResponseEnum.INVALID_PHONE.getCode();
//                message = ApiResponseEnum.INVALID_PHONE.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_EMAIL.getMessage())) {
//                code = ApiResponseEnum.DUPLICATE_EMAIL.getCode();
//                message = ApiResponseEnum.DUPLICATE_EMAIL.getMessage();
//            }
//        }
//        return ApiResponse.res(code, message, data);
//    }

    /**회원 탈퇴
     * 1) input - id
     * 2)성공시OK리턴
     * */
//    @PostMapping("/user/withdraw")
//    public ApiResponse withdraw(@RequestBody MembersDto member) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        membersService.withdraw(member);
//
//        return ApiResponse.res(code, message);
//    }

    /** 서비스 관리자 > 회원 관리 */
    /** 회원 목록 / 검색
     *  1) input - (department, name, email)
     *  2) 회원 목록 조회 / 회원 검색 결과 조회(검색 조건: 부서명, 이름, 이메일)
     *  3) 성공 시 회원 목록 리턴
     */
//    @GetMapping("/user/member/list")
//    @Operation(summary ="회원가입", description = "회원가입 ")
//    public ApiResponse memberList(String department, String name, String email) {
//        // 2) 회원 목록 조회 / 회원 검색 결과 조회(검색 조건: 이메일, 이름)
//        List<MembersDto> memberList = MembersDto.toListApiResponse(membersService.selectList(department, name, email));
//
//        // 3) 성공하면 회원 목록 리턴
//        return ApiResponse.ok(memberList);
//    }

    /** 회원 정보 조회
     *  1) input - id
     *  2) ID로 회원 정보 조회
     *  3) 성공 시 회원 정보 리턴
     */
//    @GetMapping("/svcadmin/member/mypage")
//    public ApiResponse memberInfo(@RequestParam String id) {
//        // 2) ID로 회원 정보 조회
//        Optional<MembersDto> memberInfo = membersService.selectMemberInfo(id);
//
//        // 3) 성공 시 회원 정보 리턴
//        return ApiResponse.ok(memberInfo.get().toApiResponse());
//    }

    /** 회원 정보 수정
     *  1) input - id, (name, email, phone, companyId, department, role)
     *  2) 이메일 변경 여부 확인
     *  3) 이메일 중복 검사
     *  4) 이메일 유효성 검사
     *  5) 이메일 인증 메일 전송
     *  6) 휴대폰 번호 유효성 검사
     *  7) 유효성 검사 모두 통과 시 회원 정보 수정
     *  8) 성공하면 회원 정보 리턴
     */
//    @PostMapping("/svcadmin/member/chginfo")
//    public ApiResponse updateMember(@RequestBody MembersDto changeInfo) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//        Object data = null;
//
//        try {
//            // 회원 정보 수정
//            MembersDto memberInfo = membersService.updateMember(changeInfo);
//            data = memberInfo.toApiResponse();
//
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_EMAIL.getMessage())) {
//                code = ApiResponseEnum.DUPLICATE_EMAIL.getCode();
//                message = ApiResponseEnum.DUPLICATE_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
//                code = ApiResponseEnum.INVALID_EMAIL.getCode();
//                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.INVALID_PHONE.getMessage())) {
//                code = ApiResponseEnum.INVALID_PHONE.getCode();
//                message = ApiResponseEnum.INVALID_PHONE.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        // 8) 성공하면 회원 정보 리턴
//        return ApiResponse.res(code, message, data);
//    }
//
//    /** 비밀번호 초기화
//     *  1) input - id
//     *  2) 임시 비밀번호 생성
//     *  3) 임시 비밀번호 암호화
//     *  4) 임시 비밀번호 저장
//     *  5) 임시 비밀번호 안내 메일 전송
//     *  6) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/resetpassword")
//    public ApiResponse resetPassword(@RequestBody MembersDto member) {
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 비밀번호 초기화
//            membersService.resetPassword(member.getId());
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        // 6) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 회원 가입 승인 / 거절
//     *  1) input - id, approved
//     *  2) 회원 가입 승인 / 거절 저장
//     *  3) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/approved")
//    public ApiResponse updateApproved(@RequestBody Map<String, List<String>> memList) {
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 회원 가입 승인 / 거절
//            membersService.updateApproved(memList.get("id"), memList.get("approved").get(0));
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        // 3) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 회원 잠금
//     *  1) input - id
//     *  2) 회원 잠금
//     *  3) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/lock")
//    public ApiResponse lockMember(@RequestBody Map<String, List<String>> memList) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 회원 잠금
//            membersService.lockMember(memList.get("id"));
//        } catch (Exception e) {
//            if(e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        // 3) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 회원 등록
//     *  1) input - name, email, phone, password, approved, companyId, department
//     *  2) 이메일 중복 검사
//     *  3) 이메일 유효성 검사
//     *  4) 전화번호 유효성 검사
//     *  5) 비밀번호 암호화
//     *  6) 회원 등록 정보 저장
//     *  7) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/regist")
//    public ApiResponse insertMember(@RequestBody MembersDto memberInfo) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 회원 등록
//            membersService.insertMember(memberInfo);
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_EMAIL.getMessage())) {
//                code = ApiResponseEnum.DUPLICATE_EMAIL.getCode();
//                message = ApiResponseEnum.DUPLICATE_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
//                code = ApiResponseEnum.INVALID_EMAIL.getCode();
//                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.INVALID_PHONE.getMessage())) {
//                code = ApiResponseEnum.INVALID_PHONE.getCode();
//                message = ApiResponseEnum.INVALID_PHONE.getMessage();
//            }
//        }
//        // 7) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 이메일 중복 검사
//     *  1) input - email
//     *  2) 이메일 유효성 검사
//     *  3) 이메일 중복 검사
//     *  4) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/dupliemail")
//    public ApiResponse dupleEmail(@RequestBody Map<String, String> email) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 이메일 중복 검사
//            membersService.dupleEmail(email);
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.INVALID_EMAIL.getMessage())) {
//                code = ApiResponseEnum.INVALID_EMAIL.getCode();
//                message = ApiResponseEnum.INVALID_EMAIL.getMessage();
//            }
//            if (e.getMessage().equals(ApiResponseEnum.DUPLICATE_EMAIL.getMessage())) {
//                code = ApiResponseEnum.DUPLICATE_EMAIL.getCode();
//                message = ApiResponseEnum.DUPLICATE_EMAIL.getMessage();
//            }
//        }
//        // 4) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 회원 초대
//     *  1) input - id
//     *  2) 로그인 이력 확인
//     *  3) 초대 메일 보내기
//     *  4) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/invite")
//    public ApiResponse inviteMember(@RequestBody Map<String, List<String>> memberList) {
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 회원 초대
//            membersService.inviteMember(memberList.get("id"));
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.NO_SEND_INVITE_EMAIL.getMessage())) {
//                code = ApiResponseEnum.NO_SEND_INVITE_EMAIL.getCode();
//                message = ApiResponseEnum.NO_SEND_INVITE_EMAIL.getMessage();
//            }
//        }
//        // 4) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
//
//    /** 회원 삭제 (탈퇴 처리)
//     *  1) input - id
//     *  2) 해당 ID의 회원 탈퇴 처리
//     *  3) 성공 시 성공 메세지 리턴
//     */
//    @PostMapping("/svcadmin/member/withdraw")
//    public ApiResponse deleteMem(@RequestBody Map<String, List<String>> memList) {
//
//        int code = ApiResponseEnum.OK.getCode();
//        String message = ApiResponseEnum.OK.getMessage();
//
//        try {
//            // 2) 해당 ID의 회원 탈퇴 처리
//            membersService.deleteMem(memList.get("id"));
//        } catch (Exception e) {
//            if (e.getMessage().equals(ApiResponseEnum.UNABLE_TO_PERFORM.getMessage())) {
//                code = ApiResponseEnum.UNABLE_TO_PERFORM.getCode();
//                message = ApiResponseEnum.UNABLE_TO_PERFORM.getMessage();
//            }
//        }
//        // 3) 성공 시 성공 메세지 리턴
//        return ApiResponse.res(code, message);
//    }
}