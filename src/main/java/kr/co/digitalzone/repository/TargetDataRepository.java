package kr.co.digitalzone.repository;

import kr.co.digitalzone.dto.CrawlingCountDto;
import kr.co.digitalzone.entity.TargetData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TargetDataRepository extends JpaRepository<TargetData, Long> {

    /**
     * 모든 타겟 데이터 조회 (최신순)
     */
    @Query("SELECT new kr.co.digitalzone.dto.CrawlingCountDto(td.source, td.keyword, td.targetCount) " +
            "FROM TargetData td " +
            "ORDER BY td.startDate DESC")
    List<CrawlingCountDto> findAllTargetData();

    /**
     * 특정 job_id로 타겟 데이터 조회
     */
    List<TargetData> findByJobId(String jobId);

    /**
     * 특정 job_id의 타겟 데이터 조회
     */
    @Query("SELECT td FROM TargetData td WHERE td.jobId = :jobId")
    List<TargetData> findByJobId(@Param("jobId") String jobId);

//    TargetData findByJobId(@Param("jobId") String jobId);
    
    
}