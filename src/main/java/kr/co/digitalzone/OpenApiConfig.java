package kr.co.digitalzone;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI openAPI() {
        Info info = new Info()
                .version("v1.0") // API 버전
                .title("Labeling Tool API") // API 이름
                .description("API 모음"); // API 설명
        return new OpenAPI().info(info);
    }
}
