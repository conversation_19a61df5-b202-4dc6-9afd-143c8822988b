
name: CICD

on:
  push:
    branches: ["main","dev"]
  pull_request:
    branches: ["main","dev"]




jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      # 1. 코드 체크아웃
      - name: 코드 체크아웃
        uses: actions/checkout@v3

      # 2: 권한 설정
      - name: gradlew 권한 추가
        run: chmod +x ./gradlew

      # 3. Gradle로 .jar 파일 빌드
      - name: Jar 파일 생성
        run: ./gradlew clean build -x test

      # 4. Docker 설치
      - name: 도커 설치
        uses: docker/setup-buildx-action@v2

      # 5. Docker 이미지 빌드
      - name: 도커 이미지 빌드
        run: |
          docker build --no-cache -t apfhda7/labeling_backend:latest -f Dockerfile .

      # 5. Docker Hub 로그인
      - name: 도커허브 로그인
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin

      # 6. Docker Hub에 이미지 푸시
      - name: Docker Hub에 이미지 푸시
        run: docker push apfhda7/labeling_backend:latest

      # 7. ssh key 추가
      - name: SSH key 추가
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ************** >> ~/.ssh/known_hosts


      # 8. 배포 서버에 이미지 배포 (옵션)
      - name: 배포서버에 이미지 배포
        run: |
          ssh -tt -o StrictHostKeyChecking=no ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_IP }} << EOF
            docker pull apfhda7/labeling_backend:latest
            docker stop labeling_backend || true
            docker rm labeling_backend || true
            docker run -idt --name labeling_backend --restart=always \
              -v /volume1/pj1/datalake/labeling/upload:/volume1/pj1/datalake/labeling/upload \
              -v /volume1/pj1/datalake/labeling/download:/volume1/pj1/datalake/labeling/download \
              -v /volume1/pj1/datalake/labeling/worked:/volume1/pj1/datalake/labeling/worked \
              -v /volume1/pj1/datalake/labeling/thumbnail:/volume1/pj1/datalake/labeling/thumbnail \
              -v /volume1/pj1/datalake/image:/volume1/pj1/datalake/labeling/crawling \
              -p 38080:8080 apfhda7/labeling_backend:latest
          EOF
